#pragma checksum "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\WebUI\DataMapping.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "50a8b1afe5fc82f561beeeac6e036acb0a7a4c9a60fca6daa02173d8fc08c27f"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_WebUI_DataMapping), @"mvc.1.0.view", @"/Views/WebUI/DataMapping.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.WebApi

#nullable disable
    ;
#nullable restore
#line 2 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.Domain.Entities

#nullable disable
    ;
#nullable restore
#line 3 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.Services.Interfaces

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"50a8b1afe5fc82f561beeeac6e036acb0a7a4c9a60fca6daa02173d8fc08c27f", @"/Views/WebUI/DataMapping.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"ac12188ef915a0efb6909a3d05593257fa18235f65c65f2698e4c97193ba4524", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_WebUI_DataMapping : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "0", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "1", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "2", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "3", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "4", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "5", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "6", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("mappingRuleForm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\WebUI\DataMapping.cshtml"
  
    Layout = "_Layout";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<!-- 警告容器 -->
<div id=""alerts-container""></div>

<!-- 选择API配置 -->
<div class=""row mb-4"">
    <div class=""col-md-6"">
        <div class=""card"">
            <div class=""card-header"">
                <h6 class=""m-0 font-weight-bold text-primary"">选择API配置</h6>
            </div>
            <div class=""card-body"">
                <select class=""form-select"" id=""apiConfigSelect"" onchange=""loadApiConfig()"">
                    ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "50a8b1afe5fc82f561beeeac6e036acb0a7a4c9a60fca6daa02173d8fc08c27f7420", async() => {
                WriteLiteral("请选择API配置...");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
                </select>
                <div class=""mt-3"" id=""selectedApiInfo"" style=""display: none;"">
                    <h6>API信息</h6>
                    <div id=""apiDetails""></div>
                </div>
            </div>
        </div>
    </div>
    <div class=""col-md-6"">
        <div class=""card"">
            <div class=""card-header"">
                <h6 class=""m-0 font-weight-bold text-primary"">快速操作</h6>
            </div>
            <div class=""card-body"">
                <div class=""d-grid gap-2"">
                    <button class=""btn btn-success"" onclick=""testApiAndPreview()"" id=""testApiBtn"" disabled>
                        <i class=""fas fa-play me-2""></i>测试API并预览数据
                    </button>
                    <button class=""btn btn-primary"" onclick=""saveMapping()"" id=""saveMappingBtn"" disabled>
                        <i class=""fas fa-save me-2""></i>保存映射配置
                    </button>
                    <button class=""btn btn-info"" onclick=""previewMapping()"" id=""previewMappingBtn"" di");
            WriteLiteral(@"sabled>
                        <i class=""fas fa-eye me-2""></i>预览映射结果
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据映射配置 -->
<div class=""row"">
    <!-- 源数据预览 -->
    <div class=""col-lg-6 mb-4"">
        <div class=""card shadow"">
            <div class=""card-header py-3"">
                <h6 class=""m-0 font-weight-bold text-primary"">源数据预览</h6>
            </div>
            <div class=""card-body"">
                <div id=""sourceDataPreview"">
                    <div class=""text-center text-muted"">
                        <i class=""fas fa-info-circle fa-2x mb-3""></i>
                        <p>请先选择API配置并测试获取数据</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 映射配置 -->
    <div class=""col-lg-6 mb-4"">
        <div class=""card shadow"">
            <div class=""card-header py-3 d-flex justify-content-between align-items-center"">
                <h6 class=""m-0 font-weight-bold text-primary"">映射配");
            WriteLiteral(@"置</h6>
                <button class=""btn btn-sm btn-outline-primary"" onclick=""addMappingRule()"">
                    <i class=""fas fa-plus me-1""></i>添加规则
                </button>
            </div>
            <div class=""card-body"">
                <div id=""mappingRules"">
                    <div class=""text-center text-muted"">
                        <i class=""fas fa-exchange-alt fa-2x mb-3""></i>
                        <p>暂无映射规则，点击""添加规则""开始配置</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 映射结果预览 -->
<div class=""row"">
    <div class=""col-12"">
        <div class=""card shadow"">
            <div class=""card-header py-3"">
                <h6 class=""m-0 font-weight-bold text-primary"">映射结果预览</h6>
            </div>
            <div class=""card-body"">
                <div id=""mappingResultPreview"">
                    <div class=""text-center text-muted"">
                        <i class=""fas fa-search fa-2x mb-3""></i>
                        <p>配置映射");
            WriteLiteral(@"规则后，点击""预览映射结果""查看转换效果</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加映射规则模态框 -->
<div class=""modal fade"" id=""addMappingRuleModal"" tabindex=""-1"">
    <div class=""modal-dialog modal-lg"">
        <div class=""modal-content"">
            <div class=""modal-header"">
                <h5 class=""modal-title"">添加映射规则</h5>
                <button type=""button"" class=""btn-close"" data-bs-dismiss=""modal""></button>
            </div>
            <div class=""modal-body"">
                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "50a8b1afe5fc82f561beeeac6e036acb0a7a4c9a60fca6daa02173d8fc08c27f12441", async() => {
                WriteLiteral(@"
                    <div class=""row"">
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""sourceField"" class=""form-label"">源字段路径 *</label>
                                <input type=""text"" class=""form-control"" id=""sourceField"" placeholder=""例如: $.data.items[0].name"" required>
                                <div class=""form-text"">使用JSONPath语法，如 $.data.name 或 $.items[*].id</div>
                            </div>
                        </div>
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""targetField"" class=""form-label"">目标字段名 *</label>
                                <input type=""text"" class=""form-control"" id=""targetField"" placeholder=""例如: userName"" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class=""row"">
                        <di");
                WriteLiteral(@"v class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""transformType"" class=""form-label"">转换类型</label>
                                <select class=""form-select"" id=""transformType"">
                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "50a8b1afe5fc82f561beeeac6e036acb0a7a4c9a60fca6daa02173d8fc08c27f14105", async() => {
                    WriteLiteral("无转换");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_1.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "50a8b1afe5fc82f561beeeac6e036acb0a7a4c9a60fca6daa02173d8fc08c27f15384", async() => {
                    WriteLiteral("字符串转换");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_2.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "50a8b1afe5fc82f561beeeac6e036acb0a7a4c9a60fca6daa02173d8fc08c27f16665", async() => {
                    WriteLiteral("数字转换");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_3.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "50a8b1afe5fc82f561beeeac6e036acb0a7a4c9a60fca6daa02173d8fc08c27f17945", async() => {
                    WriteLiteral("日期转换");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_4.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "50a8b1afe5fc82f561beeeac6e036acb0a7a4c9a60fca6daa02173d8fc08c27f19225", async() => {
                    WriteLiteral("布尔转换");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_5.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "50a8b1afe5fc82f561beeeac6e036acb0a7a4c9a60fca6daa02173d8fc08c27f20505", async() => {
                    WriteLiteral("JSON转换");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_6.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "50a8b1afe5fc82f561beeeac6e036acb0a7a4c9a60fca6daa02173d8fc08c27f21787", async() => {
                    WriteLiteral("数组转换");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_7.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                                </select>
                            </div>
                        </div>
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""defaultValue"" class=""form-label"">默认值</label>
                                <input type=""text"" class=""form-control"" id=""defaultValue"" placeholder=""字段为空时的默认值"">
                            </div>
                        </div>
                    </div>
                    
                    <div class=""mb-3"">
                        <label for=""transformExpression"" class=""form-label"">转换表达式</label>
                        <textarea class=""form-control"" id=""transformExpression"" rows=""3"" placeholder=""可选：自定义转换逻辑""></textarea>
                        <div class=""form-text"">支持JavaScript表达式，使用 value 变量引用源值</div>
                    </div>
                    
                    <div class=""mb-3"">
                        <div class=""form-check"">
                           ");
                WriteLiteral(@" <input class=""form-check-input"" type=""checkbox"" id=""isRequired"">
                            <label class=""form-check-label"" for=""isRequired"">
                                必填字段
                            </label>
                        </div>
                    </div>
                ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
            </div>
            <div class=""modal-footer"">
                <button type=""button"" class=""btn btn-secondary"" data-bs-dismiss=""modal"">取消</button>
                <button type=""button"" class=""btn btn-primary"" onclick=""saveMappingRule()"">添加规则</button>
            </div>
        </div>
    </div>
</div>

");
            DefineSection("Scripts", async() => {
                WriteLiteral(@"
<script>
    let currentApiConfig = null;
    let sourceData = null;
    let mappingRules = [];

    $(document).ready(function() {
        loadApiConfigs();
        
        // 检查URL参数中是否有apiId
        const urlParams = new URLSearchParams(window.location.search);
        const apiId = urlParams.get('apiId');
        if (apiId) {
            setTimeout(() => {
                $('#apiConfigSelect').val(apiId);
                loadApiConfig();
            }, 1000);
        }
    });

    function loadApiConfigs() {
        callAPI('/api/ApiConfiguration')
            .done(function(data) {
                var select = $('#apiConfigSelect');
                select.empty().append('<option value="""">请选择API配置...</option>');
                
                data.forEach(function(config) {
                    select.append(`<option value=""${config.id}"">${config.name} (${config.method} ${config.url})</option>`);
                });
            })
            .fail(function() {
                showError('加载API配置失败');
");
                WriteLiteral(@"            });
    }

    function loadApiConfig() {
        const apiId = $('#apiConfigSelect').val();
        if (!apiId) {
            $('#selectedApiInfo').hide();
            resetInterface();
            return;
        }

        callAPI('/api/ApiConfiguration/' + apiId)
            .done(function(config) {
                currentApiConfig = config;
                displayApiInfo(config);
                loadExistingMapping(apiId);
                enableButtons();
            })
            .fail(function() {
                showError('加载API配置详情失败');
            });
    }

    function displayApiInfo(config) {
        const html = `
            <div class=""row"">
                <div class=""col-6""><strong>名称:</strong><br>${config.name}</div>
                <div class=""col-6""><strong>方法:</strong><br><span class=""badge bg-primary"">${config.method}</span></div>
            </div>
            <hr>
            <div class=""row"">
                <div class=""col-12""><strong>URL:</strong><br><code>${config.url");
                WriteLiteral(@"}</code></div>
            </div>
        `;
        $('#apiDetails').html(html);
        $('#selectedApiInfo').show();
    }

    function loadExistingMapping(apiId) {
        callAPI('/api/DataMapping/api-config/' + apiId + '/mapping')
            .done(function(mapping) {
                if (mapping && mapping.mappingRules) {
                    mappingRules = mapping.mappingRules;
                    displayMappingRules();
                }
            })
            .fail(function() {
                // 没有现有映射配置，这是正常的
                mappingRules = [];
                displayMappingRules();
            });
    }

    function testApiAndPreview() {
        if (!currentApiConfig) return;

        showInfo('正在测试API...');
        callAPI('/api/ApiExecution/execute/' + currentApiConfig.id, 'POST')
            .done(function(result) {
                if (result.isSuccess) {
                    sourceData = result.responseData;
                    displaySourceData(sourceData);
                    showSuccess('");
                WriteLiteral(@"API测试成功，数据已加载');
                    $('#previewMappingBtn').prop('disabled', false);
                } else {
                    showError('API测试失败: ' + (result.errorMessage || '未知错误'));
                }
            })
            .fail(function() {
                showError('API测试请求失败');
            });
    }

    function displaySourceData(data) {
        const formattedData = JSON.stringify(data, null, 2);
        const html = `
            <div class=""mb-3"">
                <button class=""btn btn-sm btn-outline-secondary"" onclick=""copyToClipboard('${formattedData.replace(/'/g, ""\\'"")}')"">
                    <i class=""fas fa-copy me-1""></i>复制数据
                </button>
            </div>
            <pre class=""bg-light p-3 rounded"" style=""max-height: 400px; overflow-y: auto;""><code>${formattedData}</code></pre>
        `;
        $('#sourceDataPreview').html(html);
    }

    function addMappingRule() {
        $('#mappingRuleForm')[0].reset();
        $('#addMappingRuleModal').modal('show');
    }

");
                WriteLiteral(@"    function saveMappingRule() {
        const rule = {
            sourceField: $('#sourceField').val(),
            targetField: $('#targetField').val(),
            transformType: parseInt($('#transformType').val()),
            defaultValue: $('#defaultValue').val(),
            transformExpression: $('#transformExpression').val(),
            isRequired: $('#isRequired').is(':checked')
        };

        if (!rule.sourceField || !rule.targetField) {
            showError('请填写源字段路径和目标字段名');
            return;
        }

        mappingRules.push(rule);
        displayMappingRules();
        $('#addMappingRuleModal').modal('hide');
        showSuccess('映射规则添加成功');
    }

    function displayMappingRules() {
        const container = $('#mappingRules');
        
        if (mappingRules.length === 0) {
            container.html(`
                <div class=""text-center text-muted"">
                    <i class=""fas fa-exchange-alt fa-2x mb-3""></i>
                    <p>暂无映射规则，点击""添加规则""开始配置</p>
          ");
                WriteLiteral(@"      </div>
            `);
            return;
        }

        let html = '';
        mappingRules.forEach((rule, index) => {
            const transformTypeName = getTransformTypeName(rule.transformType);
            html += `
                <div class=""card mb-2"">
                    <div class=""card-body py-2"">
                        <div class=""d-flex justify-content-between align-items-center"">
                            <div>
                                <strong>${rule.targetField}</strong>
                                <br>
                                <small class=""text-muted"">${rule.sourceField} → ${transformTypeName}</small>
                            </div>
                            <div>
                                <button class=""btn btn-sm btn-outline-danger"" onclick=""removeMappingRule(${index})"">
                                    <i class=""fas fa-trash""></i>
                                </button>
                            </div>
                        </div>
      ");
                WriteLiteral(@"              </div>
                </div>
            `;
        });
        
        container.html(html);
    }

    function getTransformTypeName(type) {
        const types = {
            0: '无转换',
            1: '字符串',
            2: '数字',
            3: '日期',
            4: '布尔',
            5: 'JSON',
            6: '数组'
        };
        return types[type] || '未知';
    }

    function removeMappingRule(index) {
        if (confirm('确定要删除这个映射规则吗？')) {
            mappingRules.splice(index, 1);
            displayMappingRules();
            showSuccess('映射规则已删除');
        }
    }

    function previewMapping() {
        if (!sourceData || mappingRules.length === 0) {
            showWarning('请先测试API获取数据并配置映射规则');
            return;
        }

        const mappingConfig = {
            mappingRules: mappingRules
        };

        const previewData = {
            sourceData: sourceData,
            mappingConfig: mappingConfig
        };

        callAPI('/api/DataMapping/preview', 'POST', previe");
                WriteLiteral(@"wData)
            .done(function(result) {
                displayMappingResult(result);
                showSuccess('映射预览生成成功');
            })
            .fail(function() {
                showError('映射预览失败');
            });
    }

    function displayMappingResult(result) {
        const formattedResult = JSON.stringify(result, null, 2);
        const html = `
            <div class=""mb-3"">
                <button class=""btn btn-sm btn-outline-secondary"" onclick=""copyToClipboard('${formattedResult.replace(/'/g, ""\\'"")}')"">
                    <i class=""fas fa-copy me-1""></i>复制结果
                </button>
            </div>
            <pre class=""bg-light p-3 rounded"" style=""max-height: 400px; overflow-y: auto;""><code>${formattedResult}</code></pre>
        `;
        $('#mappingResultPreview').html(html);
    }

    function saveMapping() {
        if (!currentApiConfig || mappingRules.length === 0) {
            showWarning('请选择API配置并添加映射规则');
            return;
        }

        const mappingConfig");
                WriteLiteral(@" = {
            mappingRules: mappingRules
        };

        callAPI('/api/DataMapping/api-config/' + currentApiConfig.id + '/mapping', 'PUT', mappingConfig)
            .done(function() {
                showSuccess('映射配置保存成功');
            })
            .fail(function() {
                showError('映射配置保存失败');
            });
    }

    function enableButtons() {
        $('#testApiBtn, #saveMappingBtn').prop('disabled', false);
    }

    function resetInterface() {
        currentApiConfig = null;
        sourceData = null;
        mappingRules = [];
        $('#testApiBtn, #saveMappingBtn, #previewMappingBtn').prop('disabled', true);
        $('#sourceDataPreview').html(`
            <div class=""text-center text-muted"">
                <i class=""fas fa-info-circle fa-2x mb-3""></i>
                <p>请先选择API配置并测试获取数据</p>
            </div>
        `);
        displayMappingRules();
        $('#mappingResultPreview').html(`
            <div class=""text-center text-muted"">
                <i class=""fas ");
                WriteLiteral(@"fa-search fa-2x mb-3""></i>
                <p>配置映射规则后，点击""预览映射结果""查看转换效果</p>
            </div>
        `);
    }

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            showSuccess('已复制到剪贴板');
        });
    }

    function showInfo(message) {
        showAlert(message, 'info');
    }
</script>
");
            }
            );
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591

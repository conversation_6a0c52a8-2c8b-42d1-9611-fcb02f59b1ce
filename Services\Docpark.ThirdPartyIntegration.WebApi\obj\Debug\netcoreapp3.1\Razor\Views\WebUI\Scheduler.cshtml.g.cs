#pragma checksum "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\WebUI\Scheduler.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "40518da50b12c280dfbb2e9063b66f62e3a46491d41eb62ab9ee44d67194e1b8"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_WebUI_Scheduler), @"mvc.1.0.view", @"/Views/WebUI/Scheduler.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.WebApi

#nullable disable
    ;
#nullable restore
#line 2 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.Domain.Entities

#nullable disable
    ;
#nullable restore
#line 3 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.Services.Interfaces

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"40518da50b12c280dfbb2e9063b66f62e3a46491d41eb62ab9ee44d67194e1b8", @"/Views/WebUI/Scheduler.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"ac12188ef915a0efb6909a3d05593257fa18235f65c65f2698e4c97193ba4524", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_WebUI_Scheduler : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "0", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "1", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "2", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("createScheduleForm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\WebUI\Scheduler.cshtml"
  
    Layout = "_Layout";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<!-- 警告容器 -->
<div id=""alerts-container""></div>

<!-- 调度器状态 -->
<div class=""row mb-4"">
    <div class=""col-md-8"">
        <div class=""card"">
            <div class=""card-header"">
                <h6 class=""m-0 font-weight-bold text-primary"">调度器状态</h6>
            </div>
            <div class=""card-body"">
                <div id=""schedulerStatus"">
                    <div class=""text-center"">
                        <i class=""fas fa-spinner fa-spin""></i>
                        <p class=""mt-2"">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class=""col-md-4"">
        <div class=""card"">
            <div class=""card-header"">
                <h6 class=""m-0 font-weight-bold text-primary"">调度器控制</h6>
            </div>
            <div class=""card-body"">
                <div class=""d-grid gap-2"">
                    <button class=""btn btn-success"" onclick=""startScheduler()"">
                        <i class=""fas fa-play me-2""></i>启动调度器
             ");
            WriteLiteral(@"       </button>
                    <button class=""btn btn-warning"" onclick=""pauseScheduler()"">
                        <i class=""fas fa-pause me-2""></i>暂停调度器
                    </button>
                    <button class=""btn btn-danger"" onclick=""stopScheduler()"">
                        <i class=""fas fa-stop me-2""></i>停止调度器
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务管理 -->
<div class=""row mb-4"">
    <div class=""col-12"">
        <div class=""card shadow"">
            <div class=""card-header py-3 d-flex justify-content-between align-items-center"">
                <h6 class=""m-0 font-weight-bold text-primary"">定时任务列表</h6>
                <button class=""btn btn-primary"" data-bs-toggle=""modal"" data-bs-target=""#createScheduleModal"">
                    <i class=""fas fa-plus me-2""></i>创建定时任务
                </button>
            </div>
            <div class=""card-body"">
                <div class=""table-responsive"">
                    <table c");
            WriteLiteral(@"lass=""table table-bordered"" id=""scheduledJobsTable"">
                        <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>API配置</th>
                                <th>调度类型</th>
                                <th>调度表达式</th>
                                <th>状态</th>
                                <th>下次执行</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id=""scheduledJobsTableBody"">
                            <tr>
                                <td colspan=""7"" class=""text-center"">
                                    <i class=""fas fa-spinner fa-spin""></i>
                                    加载中...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建定时任务模态框 -->
<div class");
            WriteLiteral(@"=""modal fade"" id=""createScheduleModal"" tabindex=""-1"">
    <div class=""modal-dialog modal-lg"">
        <div class=""modal-content"">
            <div class=""modal-header"">
                <h5 class=""modal-title"">创建定时任务</h5>
                <button type=""button"" class=""btn-close"" data-bs-dismiss=""modal""></button>
            </div>
            <div class=""modal-body"">
                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "40518da50b12c280dfbb2e9063b66f62e3a46491d41eb62ab9ee44d67194e1b89461", async() => {
                WriteLiteral("\n                    <div class=\"mb-3\">\n                        <label for=\"scheduleApiConfig\" class=\"form-label\">选择API配置 *</label>\n                        <select class=\"form-select\" id=\"scheduleApiConfig\" required>\n                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "40518da50b12c280dfbb2e9063b66f62e3a46491d41eb62ab9ee44d67194e1b89998", async() => {
                    WriteLiteral("请选择API配置...");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                        </select>
                    </div>
                    
                    <div class=""mb-3"">
                        <label for=""scheduleType"" class=""form-label"">调度类型 *</label>
                        <select class=""form-select"" id=""scheduleType"" onchange=""toggleScheduleOptions()"" required>
                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "40518da50b12c280dfbb2e9063b66f62e3a46491d41eb62ab9ee44d67194e1b811608", async() => {
                    WriteLiteral("请选择调度类型...");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "40518da50b12c280dfbb2e9063b66f62e3a46491d41eb62ab9ee44d67194e1b812886", async() => {
                    WriteLiteral("间隔调度");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_1.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "40518da50b12c280dfbb2e9063b66f62e3a46491d41eb62ab9ee44d67194e1b814158", async() => {
                    WriteLiteral("每日调度");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_2.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "40518da50b12c280dfbb2e9063b66f62e3a46491d41eb62ab9ee44d67194e1b815430", async() => {
                    WriteLiteral("Cron表达式");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_3.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                        </select>
                    </div>
                    
                    <!-- 间隔调度选项 -->
                    <div id=""intervalOptions"" style=""display: none;"">
                        <div class=""row"">
                            <div class=""col-md-6"">
                                <div class=""mb-3"">
                                    <label for=""intervalValue"" class=""form-label"">间隔值</label>
                                    <input type=""number"" class=""form-control"" id=""intervalValue"" min=""1"" value=""30"">
                                </div>
                            </div>
                            <div class=""col-md-6"">
                                <div class=""mb-3"">
                                    <label for=""intervalUnit"" class=""form-label"">时间单位</label>
                                    <select class=""form-select"" id=""intervalUnit"">
                                        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "40518da50b12c280dfbb2e9063b66f62e3a46491d41eb62ab9ee44d67194e1b817649", async() => {
                    WriteLiteral("秒");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_1.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "40518da50b12c280dfbb2e9063b66f62e3a46491d41eb62ab9ee44d67194e1b818930", async() => {
                    WriteLiteral("分钟");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_2.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_2);
                BeginWriteTagHelperAttribute();
                __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                __tagHelperExecutionContext.AddHtmlAttribute("selected", Html.Raw(__tagHelperStringValueBuffer), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.Minimized);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "40518da50b12c280dfbb2e9063b66f62e3a46491d41eb62ab9ee44d67194e1b820535", async() => {
                    WriteLiteral("小时");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_3.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 每日调度选项 -->
                    <div id=""dailyOptions"" style=""display: none;"">
                        <div class=""mb-3"">
                            <label for=""dailyTime"" class=""form-label"">执行时间</label>
                            <input type=""time"" class=""form-control"" id=""dailyTime"" value=""09:00"">
                        </div>
                    </div>
                    
                    <!-- Cron表达式选项 -->
                    <div id=""cronOptions"" style=""display: none;"">
                        <div class=""mb-3"">
                            <label for=""cronExpression"" class=""form-label"">Cron表达式</label>
                            <input type=""text"" class=""form-control"" id=""cronExpression"" placeholder=""例如: 0 0 9 * * ?"">
                            <div class=""form-text"">
        ");
                WriteLiteral(@"                        格式: 秒 分 时 日 月 周<br>
                                示例: 0 0 9 * * ? (每天9点执行)
                            </div>
                        </div>
                    </div>
                    
                    <div class=""row"">
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""scheduleStartTime"" class=""form-label"">开始时间</label>
                                <input type=""datetime-local"" class=""form-control"" id=""scheduleStartTime"">
                            </div>
                        </div>
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""scheduleEndTime"" class=""form-label"">结束时间</label>
                                <input type=""datetime-local"" class=""form-control"" id=""scheduleEndTime"">
                            </div>
                        </div>
                    </div>
                    
     ");
                WriteLiteral(@"               <div class=""row"">
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""misfireInstruction"" class=""form-label"">错失触发策略</label>
                                <select class=""form-select"" id=""misfireInstruction"">
                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "40518da50b12c280dfbb2e9063b66f62e3a46491d41eb62ab9ee44d67194e1b824324", async() => {
                    WriteLiteral("忽略错失触发");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_1.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "40518da50b12c280dfbb2e9063b66f62e3a46491d41eb62ab9ee44d67194e1b825606", async() => {
                    WriteLiteral("立即执行一次");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_2.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "40518da50b12c280dfbb2e9063b66f62e3a46491d41eb62ab9ee44d67194e1b826888", async() => {
                    WriteLiteral("不执行");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_3.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                                </select>
                            </div>
                        </div>
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""maxRetryCount"" class=""form-label"">最大重试次数</label>
                                <input type=""number"" class=""form-control"" id=""maxRetryCount"" min=""0"" max=""10"" value=""3"">
                            </div>
                        </div>
                    </div>
                    
                    <div class=""mb-3"">
                        <label for=""scheduleDescription"" class=""form-label"">描述</label>
                        <textarea class=""form-control"" id=""scheduleDescription"" rows=""3"" placeholder=""任务描述...""></textarea>
                    </div>
                    
                    <div class=""mb-3"">
                        <div class=""form-check"">
                            <input class=""form-check-input"" type=""checkbox"" id=""scheduleEnabled"" checked>
  ");
                WriteLiteral("                          <label class=\"form-check-label\" for=\"scheduleEnabled\">\n                                启用此定时任务\n                            </label>\n                        </div>\n                    </div>\n                ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
            </div>
            <div class=""modal-footer"">
                <button type=""button"" class=""btn btn-secondary"" data-bs-dismiss=""modal"">取消</button>
                <button type=""button"" class=""btn btn-primary"" onclick=""createSchedule()"">创建任务</button>
            </div>
        </div>
    </div>
</div>

");
            DefineSection("Scripts", async() => {
                WriteLiteral(@"
<script>
    $(document).ready(function() {
        loadSchedulerStatus();
        loadScheduledJobs();
        loadApiConfigs();
        
        // 每30秒刷新一次状态
        setInterval(loadSchedulerStatus, 30000);
        
        // 检查URL参数中是否有apiId
        const urlParams = new URLSearchParams(window.location.search);
        const apiId = urlParams.get('apiId');
        if (apiId) {
            setTimeout(() => {
                $('#scheduleApiConfig').val(apiId);
                $('#createScheduleModal').modal('show');
            }, 1000);
        }
    });

    function loadSchedulerStatus() {
        callAPI('/api/Scheduler/statistics')
            .done(function(data) {
                displaySchedulerStatus(data);
            })
            .fail(function() {
                $('#schedulerStatus').html('<div class=""text-danger"">加载失败</div>');
            });
    }

    function displaySchedulerStatus(data) {
        const html = `
            <div class=""row"">
                <div class=""col-md-3"">
      ");
                WriteLiteral(@"              <div class=""text-center"">
                        <h4 class=""text-primary"">${data.totalJobs || 0}</h4>
                        <small class=""text-muted"">总任务数</small>
                    </div>
                </div>
                <div class=""col-md-3"">
                    <div class=""text-center"">
                        <h4 class=""text-success"">${data.runningJobs || 0}</h4>
                        <small class=""text-muted"">运行中</small>
                    </div>
                </div>
                <div class=""col-md-3"">
                    <div class=""text-center"">
                        <h4 class=""text-warning"">${data.pausedJobs || 0}</h4>
                        <small class=""text-muted"">已暂停</small>
                    </div>
                </div>
                <div class=""col-md-3"">
                    <div class=""text-center"">
                        <h4 class=""text-info"">${data.todayExecutions || 0}</h4>
                        <small class=""text-muted"">今日执行</small>
               ");
                WriteLiteral(@"     </div>
                </div>
            </div>
            <hr>
            <div class=""row"">
                <div class=""col-md-6"">
                    <strong>调度器状态:</strong><br>
                    <span class=""badge bg-success"">${data.schedulerState || '未知'}</span>
                </div>
                <div class=""col-md-6"">
                    <strong>启动时间:</strong><br>
                    <span class=""text-muted"">${formatDateTime(data.startTime)}</span>
                </div>
            </div>
        `;
        $('#schedulerStatus').html(html);
    }

    function loadScheduledJobs() {
        callAPI('/api/Scheduler/jobs')
            .done(function(data) {
                displayScheduledJobs(data);
            })
            .fail(function() {
                $('#scheduledJobsTableBody').html('<tr><td colspan=""7"" class=""text-center text-danger"">加载失败</td></tr>');
            });
    }

    function displayScheduledJobs(jobs) {
        const tbody = $('#scheduledJobsTableBody');
        tbody");
                WriteLiteral(@".empty();
        
        if (jobs.length === 0) {
            tbody.html('<tr><td colspan=""7"" class=""text-center text-muted"">暂无定时任务</td></tr>');
            return;
        }
        
        jobs.forEach(function(job) {
            const statusBadge = getJobStatusBadge(job.state);
            const scheduleType = getScheduleTypeName(job.scheduleType);
            
            const row = `
                <tr>
                    <td>${job.name || 'N/A'}</td>
                    <td>${job.apiConfigName || 'N/A'}</td>
                    <td><span class=""badge bg-info"">${scheduleType}</span></td>
                    <td><code>${job.scheduleExpression || 'N/A'}</code></td>
                    <td>${statusBadge}</td>
                    <td>${formatDateTime(job.nextFireTime)}</td>
                    <td>
                        <div class=""btn-group btn-group-sm"">
                            <button class=""btn btn-outline-success"" onclick=""triggerJob('${job.id}')"" title=""立即执行"">
                              ");
                WriteLiteral(@"  <i class=""fas fa-play""></i>
                            </button>
                            <button class=""btn btn-outline-warning"" onclick=""pauseJob('${job.id}')"" title=""暂停"">
                                <i class=""fas fa-pause""></i>
                            </button>
                            <button class=""btn btn-outline-info"" onclick=""resumeJob('${job.id}')"" title=""恢复"">
                                <i class=""fas fa-play-circle""></i>
                            </button>
                            <button class=""btn btn-outline-danger"" onclick=""deleteJob('${job.id}')"" title=""删除"">
                                <i class=""fas fa-trash""></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    function getJobStatusBadge(state) {
        const states = {
            'Normal': '<span class=""badge bg-success"">正常</span>',
            'Paused': '<span class=""badge bg");
                WriteLiteral(@"-warning"">暂停</span>',
            'Complete': '<span class=""badge bg-info"">完成</span>',
            'Error': '<span class=""badge bg-danger"">错误</span>',
            'Blocked': '<span class=""badge bg-secondary"">阻塞</span>'
        };
        return states[state] || '<span class=""badge bg-secondary"">未知</span>';
    }

    function getScheduleTypeName(type) {
        const types = {
            0: '间隔',
            1: '每日',
            2: 'Cron'
        };
        return types[type] || '未知';
    }

    function loadApiConfigs() {
        callAPI('/api/ApiConfiguration')
            .done(function(data) {
                const select = $('#scheduleApiConfig');
                select.empty().append('<option value="""">请选择API配置...</option>');
                
                data.forEach(function(config) {
                    select.append(`<option value=""${config.id}"">${config.name} (${config.method} ${config.url})</option>`);
                });
            })
            .fail(function() {
                showError('");
                WriteLiteral(@"加载API配置失败');
            });
    }

    function toggleScheduleOptions() {
        const scheduleType = $('#scheduleType').val();
        
        $('#intervalOptions, #dailyOptions, #cronOptions').hide();
        
        switch(scheduleType) {
            case '0': // 间隔调度
                $('#intervalOptions').show();
                break;
            case '1': // 每日调度
                $('#dailyOptions').show();
                break;
            case '2': // Cron表达式
                $('#cronOptions').show();
                break;
        }
    }

    function createSchedule() {
        const apiConfigId = $('#scheduleApiConfig').val();
        const scheduleType = parseInt($('#scheduleType').val());
        
        if (!apiConfigId || isNaN(scheduleType)) {
            showError('请选择API配置和调度类型');
            return;
        }
        
        let scheduleConfig = {
            scheduleType: scheduleType,
            startTime: $('#scheduleStartTime').val() ? new Date($('#scheduleStartTime').val()).toISOSt");
                WriteLiteral(@"ring() : null,
            endTime: $('#scheduleEndTime').val() ? new Date($('#scheduleEndTime').val()).toISOString() : null,
            misfireInstruction: parseInt($('#misfireInstruction').val()),
            maxRetryCount: parseInt($('#maxRetryCount').val()),
            description: $('#scheduleDescription').val(),
            isEnabled: $('#scheduleEnabled').is(':checked')
        };
        
        // 根据调度类型设置特定参数
        switch(scheduleType) {
            case 0: // 间隔调度
                scheduleConfig.intervalValue = parseInt($('#intervalValue').val());
                scheduleConfig.intervalUnit = parseInt($('#intervalUnit').val());
                break;
            case 1: // 每日调度
                scheduleConfig.dailyTime = $('#dailyTime').val();
                break;
            case 2: // Cron表达式
                scheduleConfig.cronExpression = $('#cronExpression').val();
                if (!scheduleConfig.cronExpression) {
                    showError('请输入Cron表达式');
                    return;");
                WriteLiteral(@"
                }
                break;
        }
        
        callAPI('/api/Scheduler/schedule/' + apiConfigId, 'POST', scheduleConfig)
            .done(function() {
                showSuccess('定时任务创建成功');
                $('#createScheduleModal').modal('hide');
                $('#createScheduleForm')[0].reset();
                loadScheduledJobs();
                loadSchedulerStatus();
            })
            .fail(function(xhr) {
                const message = xhr.responseJSON ? xhr.responseJSON.message : '创建失败';
                showError('创建定时任务失败: ' + message);
            });
    }

    function startScheduler() {
        callAPI('/api/Scheduler/start', 'POST')
            .done(function() {
                showSuccess('调度器启动成功');
                loadSchedulerStatus();
            })
            .fail(function() {
                showError('调度器启动失败');
            });
    }

    function pauseScheduler() {
        callAPI('/api/Scheduler/pause', 'POST')
            .done(function() {
      ");
                WriteLiteral(@"          showSuccess('调度器暂停成功');
                loadSchedulerStatus();
            })
            .fail(function() {
                showError('调度器暂停失败');
            });
    }

    function stopScheduler() {
        if (!confirm('确定要停止调度器吗？这将停止所有定时任务。')) {
            return;
        }
        
        callAPI('/api/Scheduler/stop', 'POST')
            .done(function() {
                showSuccess('调度器停止成功');
                loadSchedulerStatus();
            })
            .fail(function() {
                showError('调度器停止失败');
            });
    }

    function triggerJob(jobId) {
        callAPI('/api/Scheduler/trigger/' + jobId, 'POST')
            .done(function() {
                showSuccess('任务触发成功');
                loadScheduledJobs();
            })
            .fail(function() {
                showError('任务触发失败');
            });
    }

    function pauseJob(jobId) {
        callAPI('/api/Scheduler/pause/' + jobId, 'POST')
            .done(function() {
                showSuccess('任务暂停成功')");
                WriteLiteral(@";
                loadScheduledJobs();
            })
            .fail(function() {
                showError('任务暂停失败');
            });
    }

    function resumeJob(jobId) {
        callAPI('/api/Scheduler/resume/' + jobId, 'POST')
            .done(function() {
                showSuccess('任务恢复成功');
                loadScheduledJobs();
            })
            .fail(function() {
                showError('任务恢复失败');
            });
    }

    function deleteJob(jobId) {
        if (!confirm('确定要删除这个定时任务吗？此操作不可恢复。')) {
            return;
        }
        
        callAPI('/api/Scheduler/delete/' + jobId, 'DELETE')
            .done(function() {
                showSuccess('任务删除成功');
                loadScheduledJobs();
                loadSchedulerStatus();
            })
            .fail(function() {
                showError('任务删除失败');
            });
    }
</script>
");
            }
            );
            WriteLiteral("\n<style>\n    .table th {\n        background-color: #f8f9fa;\n        font-weight: 600;\n    }\n    \n    .btn-group-sm .btn {\n        padding: 0.25rem 0.5rem;\n    }\n</style>\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591

#pragma checksum "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\WebUI\ApiConfigs.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "67485d792d0bf650623119853cb67f9d3cb4c343f703a23499e96e980242528f"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_WebUI_ApiConfigs), @"mvc.1.0.view", @"/Views/WebUI/ApiConfigs.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.WebApi

#nullable disable
    ;
#nullable restore
#line 2 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.Domain.Entities

#nullable disable
    ;
#nullable restore
#line 3 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.Services.Interfaces

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"67485d792d0bf650623119853cb67f9d3cb4c343f703a23499e96e980242528f", @"/Views/WebUI/ApiConfigs.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"ac12188ef915a0efb6909a3d05593257fa18235f65c65f2698e4c97193ba4524", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_WebUI_ApiConfigs : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "GET", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "POST", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "PUT", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "DELETE", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "PATCH", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("createApiForm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("editApiForm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\WebUI\ApiConfigs.cshtml"
  
    Layout = "_Layout";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<!-- 警告容器 -->
<div id=""alerts-container""></div>

<!-- 操作按钮 -->
<div class=""d-flex justify-content-between align-items-center mb-3"">
    <div>
        <button class=""btn btn-primary"" data-bs-toggle=""modal"" data-bs-target=""#createApiModal"">
            <i class=""fas fa-plus me-2""></i>创建新API配置
        </button>
        <button class=""btn btn-success"" onclick=""refreshApiList()"">
            <i class=""fas fa-sync-alt me-2""></i>刷新
        </button>
    </div>
    <div>
        <input type=""text"" class=""form-control"" id=""searchInput"" placeholder=""搜索API配置..."" style=""width: 300px;"">
    </div>
</div>

<!-- API配置列表 -->
<div class=""card shadow"">
    <div class=""card-header py-3"">
        <h6 class=""m-0 font-weight-bold text-primary"">API配置列表</h6>
    </div>
    <div class=""card-body"">
        <div class=""table-responsive"">
            <table class=""table table-bordered"" id=""apiConfigsTable"">
                <thead>
                    <tr>
                        <th>名称</th>
                        <th>URL</th>
        ");
            WriteLiteral(@"                <th>方法</th>
                        <th>状态</th>
                        <th>认证类型</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id=""apiConfigsTableBody"">
                    <tr>
                        <td colspan=""7"" class=""text-center"">
                            <i class=""fas fa-spinner fa-spin""></i>
                            加载中...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 创建API配置模态框 -->
<div class=""modal fade"" id=""createApiModal"" tabindex=""-1"">
    <div class=""modal-dialog modal-lg"">
        <div class=""modal-content"">
            <div class=""modal-header"">
                <h5 class=""modal-title"">创建API配置</h5>
                <button type=""button"" class=""btn-close"" data-bs-dismiss=""modal""></button>
            </div>
            <div class=""modal-body"">
                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67485d792d0bf650623119853cb67f9d3cb4c343f703a23499e96e980242528f8596", async() => {
                WriteLiteral(@"
                    <div class=""row"">
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""apiName"" class=""form-label"">API名称 *</label>
                                <input type=""text"" class=""form-control"" id=""apiName"" required>
                            </div>
                        </div>
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""apiMethod"" class=""form-label"">HTTP方法 *</label>
                                <select class=""form-select"" id=""apiMethod"" required>
                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67485d792d0bf650623119853cb67f9d3cb4c343f703a23499e96e980242528f9591", async() => {
                    WriteLiteral("GET");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67485d792d0bf650623119853cb67f9d3cb4c343f703a23499e96e980242528f10869", async() => {
                    WriteLiteral("POST");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_1.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67485d792d0bf650623119853cb67f9d3cb4c343f703a23499e96e980242528f12149", async() => {
                    WriteLiteral("PUT");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_2.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67485d792d0bf650623119853cb67f9d3cb4c343f703a23499e96e980242528f13428", async() => {
                    WriteLiteral("DELETE");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_3.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67485d792d0bf650623119853cb67f9d3cb4c343f703a23499e96e980242528f14710", async() => {
                    WriteLiteral("PATCH");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_4.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class=""mb-3"">
                        <label for=""apiUrl"" class=""form-label"">API URL *</label>
                        <input type=""url"" class=""form-control"" id=""apiUrl"" required>
                    </div>
                    
                    <div class=""mb-3"">
                        <label for=""apiDescription"" class=""form-label"">描述</label>
                        <textarea class=""form-control"" id=""apiDescription"" rows=""3""></textarea>
                    </div>
                    
                    <div class=""row"">
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""apiTimeout"" class=""form-label"">超时时间(秒)</label>
                                <input type=""number"" class=""form-control"" id=""apiTimeout"" value=""30"" min=""1"" max=""300"">
      ");
                WriteLiteral(@"                      </div>
                        </div>
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""apiRetryCount"" class=""form-label"">重试次数</label>
                                <input type=""number"" class=""form-control"" id=""apiRetryCount"" value=""3"" min=""0"" max=""10"">
                            </div>
                        </div>
                    </div>
                    
                    <div class=""mb-3"">
                        <div class=""form-check"">
                            <input class=""form-check-input"" type=""checkbox"" id=""apiIsEnabled"" checked>
                            <label class=""form-check-label"" for=""apiIsEnabled"">
                                启用此API配置
                            </label>
                        </div>
                    </div>
                ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
            </div>
            <div class=""modal-footer"">
                <button type=""button"" class=""btn btn-secondary"" data-bs-dismiss=""modal"">取消</button>
                <button type=""button"" class=""btn btn-primary"" onclick=""createApiConfig()"">创建</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑API配置模态框 -->
<div class=""modal fade"" id=""editApiModal"" tabindex=""-1"">
    <div class=""modal-dialog modal-lg"">
        <div class=""modal-content"">
            <div class=""modal-header"">
                <h5 class=""modal-title"">编辑API配置</h5>
                <button type=""button"" class=""btn-close"" data-bs-dismiss=""modal""></button>
            </div>
            <div class=""modal-body"">
                ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67485d792d0bf650623119853cb67f9d3cb4c343f703a23499e96e980242528f19807", async() => {
                WriteLiteral(@"
                    <input type=""hidden"" id=""editApiId"">
                    <!-- 表单字段与创建表单相同 -->
                    <div class=""row"">
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""editApiName"" class=""form-label"">API名称 *</label>
                                <input type=""text"" class=""form-control"" id=""editApiName"" required>
                            </div>
                        </div>
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""editApiMethod"" class=""form-label"">HTTP方法 *</label>
                                <select class=""form-select"" id=""editApiMethod"" required>
                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67485d792d0bf650623119853cb67f9d3cb4c343f703a23499e96e980242528f20921", async() => {
                    WriteLiteral("GET");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67485d792d0bf650623119853cb67f9d3cb4c343f703a23499e96e980242528f22200", async() => {
                    WriteLiteral("POST");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_1.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67485d792d0bf650623119853cb67f9d3cb4c343f703a23499e96e980242528f23480", async() => {
                    WriteLiteral("PUT");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_2.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67485d792d0bf650623119853cb67f9d3cb4c343f703a23499e96e980242528f24759", async() => {
                    WriteLiteral("DELETE");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_3.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67485d792d0bf650623119853cb67f9d3cb4c343f703a23499e96e980242528f26041", async() => {
                    WriteLiteral("PATCH");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_4.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class=""mb-3"">
                        <label for=""editApiUrl"" class=""form-label"">API URL *</label>
                        <input type=""url"" class=""form-control"" id=""editApiUrl"" required>
                    </div>
                    
                    <div class=""mb-3"">
                        <label for=""editApiDescription"" class=""form-label"">描述</label>
                        <textarea class=""form-control"" id=""editApiDescription"" rows=""3""></textarea>
                    </div>
                    
                    <div class=""row"">
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""editApiTimeout"" class=""form-label"">超时时间(秒)</label>
                                <input type=""number"" class=""form-control"" id=""editApiTimeout"" min=""1"" max=");
                WriteLiteral(@"""300"">
                            </div>
                        </div>
                        <div class=""col-md-6"">
                            <div class=""mb-3"">
                                <label for=""editApiRetryCount"" class=""form-label"">重试次数</label>
                                <input type=""number"" class=""form-control"" id=""editApiRetryCount"" min=""0"" max=""10"">
                            </div>
                        </div>
                    </div>
                    
                    <div class=""mb-3"">
                        <div class=""form-check"">
                            <input class=""form-check-input"" type=""checkbox"" id=""editApiIsEnabled"">
                            <label class=""form-check-label"" for=""editApiIsEnabled"">
                                启用此API配置
                            </label>
                        </div>
                    </div>
                ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
            </div>
            <div class=""modal-footer"">
                <button type=""button"" class=""btn btn-secondary"" data-bs-dismiss=""modal"">取消</button>
                <button type=""button"" class=""btn btn-primary"" onclick=""updateApiConfig()"">保存</button>
            </div>
        </div>
    </div>
</div>

");
            DefineSection("Scripts", async() => {
                WriteLiteral(@"
<script>
    $(document).ready(function() {
        loadApiConfigs();
        
        // 搜索功能
        $('#searchInput').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('#apiConfigsTableBody tr').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
        });
    });

    function loadApiConfigs() {
        callAPI('/api/ApiConfiguration')
            .done(function(data) {
                displayApiConfigs(data);
            })
            .fail(function() {
                $('#apiConfigsTableBody').html('<tr><td colspan=""7"" class=""text-center text-danger"">加载失败</td></tr>');
            });
    }

    function displayApiConfigs(configs) {
        var tbody = $('#apiConfigsTableBody');
        tbody.empty();
        
        if (configs.length === 0) {
            tbody.html('<tr><td colspan=""7"" class=""text-center text-muted"">暂无API配置</td></tr>');
            return;
        }
        
        configs.");
                WriteLiteral(@"forEach(function(config) {
            var statusBadge = config.isEnabled 
                ? '<span class=""badge bg-success"">启用</span>' 
                : '<span class=""badge bg-secondary"">禁用</span>';
                
            var authType = getAuthTypeName(config.authenticationConfigId);
            
            var row = `
                <tr>
                    <td>${config.name || 'N/A'}</td>
                    <td><code>${config.url || 'N/A'}</code></td>
                    <td><span class=""badge bg-primary"">${config.method || 'GET'}</span></td>
                    <td>${statusBadge}</td>
                    <td>${authType}</td>
                    <td>${formatDateTime(config.createdAt)}</td>
                    <td>
                        <div class=""btn-group btn-group-sm"">
                            <button class=""btn btn-outline-primary"" onclick=""editApiConfig('${config.id}')"">
                                <i class=""fas fa-edit""></i>
                            </button>
                   ");
                WriteLiteral(@"         <button class=""btn btn-outline-success"" onclick=""testApiConfig('${config.id}')"">
                                <i class=""fas fa-play""></i>
                            </button>
                            <button class=""btn btn-outline-info"" onclick=""configureMapping('${config.id}')"">
                                <i class=""fas fa-exchange-alt""></i>
                            </button>
                            <button class=""btn btn-outline-warning"" onclick=""configureSchedule('${config.id}')"">
                                <i class=""fas fa-clock""></i>
                            </button>
                            <button class=""btn btn-outline-danger"" onclick=""deleteApiConfig('${config.id}')"">
                                <i class=""fas fa-trash""></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    function getAuthTypeName(authConfigId) {
        if ");
                WriteLiteral(@"(!authConfigId) return '<span class=""text-muted"">无认证</span>';
        return '<span class=""badge bg-info"">已配置</span>';
    }

    function createApiConfig() {
        var formData = {
            name: $('#apiName').val(),
            url: $('#apiUrl').val(),
            method: $('#apiMethod').val(),
            description: $('#apiDescription').val(),
            timeoutSeconds: parseInt($('#apiTimeout').val()) || 30,
            retryCount: parseInt($('#apiRetryCount').val()) || 3,
            isEnabled: $('#apiIsEnabled').is(':checked')
        };
        
        if (!formData.name || !formData.url) {
            showError('请填写必填字段');
            return;
        }
        
        callAPI('/api/ApiConfiguration', 'POST', formData)
            .done(function(data) {
                showSuccess('API配置创建成功');
                $('#createApiModal').modal('hide');
                $('#createApiForm')[0].reset();
                loadApiConfigs();
            })
            .fail(function(xhr) {
                va");
                WriteLiteral(@"r message = xhr.responseJSON ? xhr.responseJSON.message : '创建失败';
                showError('创建失败: ' + message);
            });
    }

    function editApiConfig(id) {
        callAPI('/api/ApiConfiguration/' + id)
            .done(function(config) {
                $('#editApiId').val(config.id);
                $('#editApiName').val(config.name);
                $('#editApiUrl').val(config.url);
                $('#editApiMethod').val(config.method);
                $('#editApiDescription').val(config.description);
                $('#editApiTimeout').val(config.timeoutSeconds);
                $('#editApiRetryCount').val(config.retryCount);
                $('#editApiIsEnabled').prop('checked', config.isEnabled);
                
                $('#editApiModal').modal('show');
            })
            .fail(function() {
                showError('获取API配置详情失败');
            });
    }

    function updateApiConfig() {
        var id = $('#editApiId').val();
        var formData = {
            id: id,
");
                WriteLiteral(@"            name: $('#editApiName').val(),
            url: $('#editApiUrl').val(),
            method: $('#editApiMethod').val(),
            description: $('#editApiDescription').val(),
            timeoutSeconds: parseInt($('#editApiTimeout').val()) || 30,
            retryCount: parseInt($('#editApiRetryCount').val()) || 3,
            isEnabled: $('#editApiIsEnabled').is(':checked')
        };
        
        callAPI('/api/ApiConfiguration/' + id, 'PUT', formData)
            .done(function(data) {
                showSuccess('API配置更新成功');
                $('#editApiModal').modal('hide');
                loadApiConfigs();
            })
            .fail(function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '更新失败';
                showError('更新失败: ' + message);
            });
    }

    function deleteApiConfig(id) {
        if (!confirm('确定要删除这个API配置吗？此操作不可恢复。')) {
            return;
        }
        
        callAPI('/api/ApiConfiguration/' + id, 'DELETE')
    ");
                WriteLiteral(@"        .done(function() {
                showSuccess('API配置删除成功');
                loadApiConfigs();
            })
            .fail(function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '删除失败';
                showError('删除失败: ' + message);
            });
    }

    function testApiConfig(id) {
        callAPI('/api/ApiExecution/execute/' + id, 'POST')
            .done(function(result) {
                if (result.isSuccess) {
                    showSuccess('API测试成功');
                } else {
                    showWarning('API测试失败: ' + (result.errorMessage || '未知错误'));
                }
            })
            .fail(function() {
                showError('API测试请求失败');
            });
    }

    function configureMapping(id) {
        window.location.href = '/data-mapping?apiId=' + id;
    }

    function configureSchedule(id) {
        window.location.href = '/scheduler?apiId=' + id;
    }

    function refreshApiList() {
        loadApiConfigs();
        sho");
                WriteLiteral("wSuccess(\'列表已刷新\');\n    }\n</script>\n");
            }
            );
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591

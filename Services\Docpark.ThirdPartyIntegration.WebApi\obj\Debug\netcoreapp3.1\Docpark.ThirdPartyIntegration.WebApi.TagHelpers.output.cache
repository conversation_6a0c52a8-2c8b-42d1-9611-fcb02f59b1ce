﻿[{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.Razor","CaseSensitive":false,"TagMatchingRules":[{"TagName":"body"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper","Common.TypeNameIdentifier":"BodyTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.Razor","CaseSensitive":false,"TagMatchingRules":[{"TagName":"head"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper","Common.TypeNameIdentifier":"HeadTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.Razor","CaseSensitive":false,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"itemid","Value":"~/","ValueComparison":2}]},{"TagName":"a","Attributes":[{"Name":"href","Value":"~/","ValueComparison":2}]},{"TagName":"applet","Attributes":[{"Name":"archive","Value":"~/","ValueComparison":2}]},{"TagName":"area","TagStructure":2,"Attributes":[{"Name":"href","Value":"~/","ValueComparison":2}]},{"TagName":"audio","Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"base","TagStructure":2,"Attributes":[{"Name":"href","Value":"~/","ValueComparison":2}]},{"TagName":"blockquote","Attributes":[{"Name":"cite","Value":"~/","ValueComparison":2}]},{"TagName":"button","Attributes":[{"Name":"formaction","Value":"~/","ValueComparison":2}]},{"TagName":"del","Attributes":[{"Name":"cite","Value":"~/","ValueComparison":2}]},{"TagName":"embed","TagStructure":2,"Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"form","Attributes":[{"Name":"action","Value":"~/","ValueComparison":2}]},{"TagName":"html","Attributes":[{"Name":"manifest","Value":"~/","ValueComparison":2}]},{"TagName":"iframe","Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"img","TagStructure":2,"Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"img","TagStructure":2,"Attributes":[{"Name":"srcset","Value":"~/","ValueComparison":2}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"formaction","Value":"~/","ValueComparison":2}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"ins","Attributes":[{"Name":"cite","Value":"~/","ValueComparison":2}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"href","Value":"~/","ValueComparison":2}]},{"TagName":"menuitem","Attributes":[{"Name":"icon","Value":"~/","ValueComparison":2}]},{"TagName":"object","Attributes":[{"Name":"archive","Value":"~/","ValueComparison":2}]},{"TagName":"object","Attributes":[{"Name":"data","Value":"~/","ValueComparison":2}]},{"TagName":"q","Attributes":[{"Name":"cite","Value":"~/","ValueComparison":2}]},{"TagName":"script","Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"source","TagStructure":2,"Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"source","TagStructure":2,"Attributes":[{"Name":"srcset","Value":"~/","ValueComparison":2}]},{"TagName":"track","TagStructure":2,"Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"video","Attributes":[{"Name":"poster","Value":"~/","ValueComparison":2}]},{"TagName":"video","Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper","Common.TypeNameIdentifier":"UrlResolutionTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"a","Attributes":[{"Name":"asp-action"}]},{"TagName":"a","Attributes":[{"Name":"asp-all-route-data"}]},{"TagName":"a","Attributes":[{"Name":"asp-area"}]},{"TagName":"a","Attributes":[{"Name":"asp-controller"}]},{"TagName":"a","Attributes":[{"Name":"asp-fragment"}]},{"TagName":"a","Attributes":[{"Name":"asp-host"}]},{"TagName":"a","Attributes":[{"Name":"asp-page"}]},{"TagName":"a","Attributes":[{"Name":"asp-page-handler"}]},{"TagName":"a","Attributes":[{"Name":"asp-protocol"}]},{"TagName":"a","Attributes":[{"Name":"asp-route"}]},{"TagName":"a","Attributes":[{"Name":"asp-route-","NameComparison":1}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-action","TypeName":"System.String","Metadata":{"Common.PropertyName":"Action"}},{"Kind":"ITagHelper","Name":"asp-area","TypeName":"System.String","Metadata":{"Common.PropertyName":"Area"}},{"Kind":"ITagHelper","Name":"asp-controller","TypeName":"System.String","Metadata":{"Common.PropertyName":"Controller"}},{"Kind":"ITagHelper","Name":"asp-fragment","TypeName":"System.String","Metadata":{"Common.PropertyName":"Fragment"}},{"Kind":"ITagHelper","Name":"asp-host","TypeName":"System.String","Metadata":{"Common.PropertyName":"Host"}},{"Kind":"ITagHelper","Name":"asp-page","TypeName":"System.String","Metadata":{"Common.PropertyName":"Page"}},{"Kind":"ITagHelper","Name":"asp-page-handler","TypeName":"System.String","Metadata":{"Common.PropertyName":"PageHandler"}},{"Kind":"ITagHelper","Name":"asp-protocol","TypeName":"System.String","Metadata":{"Common.PropertyName":"Protocol"}},{"Kind":"ITagHelper","Name":"asp-route","TypeName":"System.String","Metadata":{"Common.PropertyName":"Route"}},{"Kind":"ITagHelper","Name":"asp-all-route-data","TypeName":"System.Collections.Generic.IDictionary<System.String, System.String>","IndexerNamePrefix":"asp-route-","IndexerTypeName":"System.String","Metadata":{"Common.PropertyName":"RouteValues"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper","Common.TypeNameIdentifier":"AnchorTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"cache"}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"priority","TypeName":"Microsoft.Extensions.Caching.Memory.CacheItemPriority?","Metadata":{"Common.PropertyName":"Priority"}},{"Kind":"ITagHelper","Name":"enabled","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"Enabled"}},{"Kind":"ITagHelper","Name":"expires-after","TypeName":"System.TimeSpan?","Metadata":{"Common.PropertyName":"ExpiresAfter"}},{"Kind":"ITagHelper","Name":"expires-on","TypeName":"System.DateTimeOffset?","Metadata":{"Common.PropertyName":"ExpiresOn"}},{"Kind":"ITagHelper","Name":"expires-sliding","TypeName":"System.TimeSpan?","Metadata":{"Common.PropertyName":"ExpiresSliding"}},{"Kind":"ITagHelper","Name":"vary-by","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryBy"}},{"Kind":"ITagHelper","Name":"vary-by-cookie","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByCookie"}},{"Kind":"ITagHelper","Name":"vary-by-culture","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"VaryByCulture"}},{"Kind":"ITagHelper","Name":"vary-by-header","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByHeader"}},{"Kind":"ITagHelper","Name":"vary-by-query","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByQuery"}},{"Kind":"ITagHelper","Name":"vary-by-route","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByRoute"}},{"Kind":"ITagHelper","Name":"vary-by-user","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"VaryByUser"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelper","Common.TypeNameIdentifier":"CacheTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.ComponentTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"component","TagStructure":2,"Attributes":[{"Name":"type"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"type","TypeName":"System.Type","Metadata":{"Common.PropertyName":"ComponentType"}},{"Kind":"ITagHelper","Name":"params","TypeName":"System.Collections.Generic.IDictionary<System.String, System.Object>","IndexerNamePrefix":"param-","IndexerTypeName":"System.Object","Metadata":{"Common.PropertyName":"Parameters"}},{"Kind":"ITagHelper","Name":"render-mode","TypeName":"Microsoft.AspNetCore.Mvc.Rendering.RenderMode","IsEnum":true,"Metadata":{"Common.PropertyName":"RenderMode"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.ComponentTagHelper","Common.TypeNameIdentifier":"ComponentTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.DistributedCacheTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"distributed-cache","Attributes":[{"Name":"name"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"name","TypeName":"System.String","Metadata":{"Common.PropertyName":"Name"}},{"Kind":"ITagHelper","Name":"enabled","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"Enabled"}},{"Kind":"ITagHelper","Name":"expires-after","TypeName":"System.TimeSpan?","Metadata":{"Common.PropertyName":"ExpiresAfter"}},{"Kind":"ITagHelper","Name":"expires-on","TypeName":"System.DateTimeOffset?","Metadata":{"Common.PropertyName":"ExpiresOn"}},{"Kind":"ITagHelper","Name":"expires-sliding","TypeName":"System.TimeSpan?","Metadata":{"Common.PropertyName":"ExpiresSliding"}},{"Kind":"ITagHelper","Name":"vary-by","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryBy"}},{"Kind":"ITagHelper","Name":"vary-by-cookie","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByCookie"}},{"Kind":"ITagHelper","Name":"vary-by-culture","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"VaryByCulture"}},{"Kind":"ITagHelper","Name":"vary-by-header","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByHeader"}},{"Kind":"ITagHelper","Name":"vary-by-query","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByQuery"}},{"Kind":"ITagHelper","Name":"vary-by-route","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByRoute"}},{"Kind":"ITagHelper","Name":"vary-by-user","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"VaryByUser"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.DistributedCacheTagHelper","Common.TypeNameIdentifier":"DistributedCacheTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"environment"}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"exclude","TypeName":"System.String","Metadata":{"Common.PropertyName":"Exclude"}},{"Kind":"ITagHelper","Name":"include","TypeName":"System.String","Metadata":{"Common.PropertyName":"Include"}},{"Kind":"ITagHelper","Name":"names","TypeName":"System.String","Metadata":{"Common.PropertyName":"Names"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper","Common.TypeNameIdentifier":"EnvironmentTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"button","Attributes":[{"Name":"asp-action"}]},{"TagName":"button","Attributes":[{"Name":"asp-all-route-data"}]},{"TagName":"button","Attributes":[{"Name":"asp-area"}]},{"TagName":"button","Attributes":[{"Name":"asp-controller"}]},{"TagName":"button","Attributes":[{"Name":"asp-fragment"}]},{"TagName":"button","Attributes":[{"Name":"asp-page"}]},{"TagName":"button","Attributes":[{"Name":"asp-page-handler"}]},{"TagName":"button","Attributes":[{"Name":"asp-route"}]},{"TagName":"button","Attributes":[{"Name":"asp-route-","NameComparison":1}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-action"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-all-route-data"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-area"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-controller"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-fragment"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-page"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-page-handler"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-route"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-route-","NameComparison":1}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-action"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-all-route-data"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-area"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-controller"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-fragment"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-page"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-page-handler"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-route"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-route-","NameComparison":1}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-action","TypeName":"System.String","Metadata":{"Common.PropertyName":"Action"}},{"Kind":"ITagHelper","Name":"asp-area","TypeName":"System.String","Metadata":{"Common.PropertyName":"Area"}},{"Kind":"ITagHelper","Name":"asp-controller","TypeName":"System.String","Metadata":{"Common.PropertyName":"Controller"}},{"Kind":"ITagHelper","Name":"asp-fragment","TypeName":"System.String","Metadata":{"Common.PropertyName":"Fragment"}},{"Kind":"ITagHelper","Name":"asp-page","TypeName":"System.String","Metadata":{"Common.PropertyName":"Page"}},{"Kind":"ITagHelper","Name":"asp-page-handler","TypeName":"System.String","Metadata":{"Common.PropertyName":"PageHandler"}},{"Kind":"ITagHelper","Name":"asp-route","TypeName":"System.String","Metadata":{"Common.PropertyName":"Route"}},{"Kind":"ITagHelper","Name":"asp-all-route-data","TypeName":"System.Collections.Generic.IDictionary<System.String, System.String>","IndexerNamePrefix":"asp-route-","IndexerTypeName":"System.String","Metadata":{"Common.PropertyName":"RouteValues"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper","Common.TypeNameIdentifier":"FormActionTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"form"}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-action","TypeName":"System.String","Metadata":{"Common.PropertyName":"Action"}},{"Kind":"ITagHelper","Name":"asp-antiforgery","TypeName":"System.Boolean?","Metadata":{"Common.PropertyName":"Antiforgery"}},{"Kind":"ITagHelper","Name":"asp-area","TypeName":"System.String","Metadata":{"Common.PropertyName":"Area"}},{"Kind":"ITagHelper","Name":"asp-controller","TypeName":"System.String","Metadata":{"Common.PropertyName":"Controller"}},{"Kind":"ITagHelper","Name":"asp-fragment","TypeName":"System.String","Metadata":{"Common.PropertyName":"Fragment"}},{"Kind":"ITagHelper","Name":"method","TypeName":"System.String","Metadata":{"Common.PropertyName":"Method"}},{"Kind":"ITagHelper","Name":"asp-page","TypeName":"System.String","Metadata":{"Common.PropertyName":"Page"}},{"Kind":"ITagHelper","Name":"asp-page-handler","TypeName":"System.String","Metadata":{"Common.PropertyName":"PageHandler"}},{"Kind":"ITagHelper","Name":"asp-route","TypeName":"System.String","Metadata":{"Common.PropertyName":"Route"}},{"Kind":"ITagHelper","Name":"asp-all-route-data","TypeName":"System.Collections.Generic.IDictionary<System.String, System.String>","IndexerNamePrefix":"asp-route-","IndexerTypeName":"System.String","Metadata":{"Common.PropertyName":"RouteValues"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper","Common.TypeNameIdentifier":"FormTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.ImageTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"img","TagStructure":2,"Attributes":[{"Name":"asp-append-version"},{"Name":"src"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-append-version","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"AppendVersion"}},{"Kind":"ITagHelper","Name":"src","TypeName":"System.String","Metadata":{"Common.PropertyName":"Src"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.ImageTagHelper","Common.TypeNameIdentifier":"ImageTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"asp-for"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-for","TypeName":"Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression","Metadata":{"Common.PropertyName":"For"}},{"Kind":"ITagHelper","Name":"asp-format","TypeName":"System.String","Metadata":{"Common.PropertyName":"Format"}},{"Kind":"ITagHelper","Name":"type","TypeName":"System.String","Metadata":{"Common.PropertyName":"InputTypeName"}},{"Kind":"ITagHelper","Name":"name","TypeName":"System.String","Metadata":{"Common.PropertyName":"Name"}},{"Kind":"ITagHelper","Name":"value","TypeName":"System.String","Metadata":{"Common.PropertyName":"Value"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper","Common.TypeNameIdentifier":"InputTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.LabelTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"label","Attributes":[{"Name":"asp-for"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-for","TypeName":"Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression","Metadata":{"Common.PropertyName":"For"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.LabelTagHelper","Common.TypeNameIdentifier":"LabelTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-append-version"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-fallback-href"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-fallback-href-exclude"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-fallback-href-include"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-fallback-test-class"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-fallback-test-property"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-fallback-test-value"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-href-exclude"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-href-include"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-append-version","TypeName":"System.Boolean?","Metadata":{"Common.PropertyName":"AppendVersion"}},{"Kind":"ITagHelper","Name":"asp-fallback-href","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackHref"}},{"Kind":"ITagHelper","Name":"asp-fallback-href-exclude","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackHrefExclude"}},{"Kind":"ITagHelper","Name":"asp-fallback-href-include","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackHrefInclude"}},{"Kind":"ITagHelper","Name":"asp-fallback-test-class","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackTestClass"}},{"Kind":"ITagHelper","Name":"asp-fallback-test-property","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackTestProperty"}},{"Kind":"ITagHelper","Name":"asp-fallback-test-value","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackTestValue"}},{"Kind":"ITagHelper","Name":"href","TypeName":"System.String","Metadata":{"Common.PropertyName":"Href"}},{"Kind":"ITagHelper","Name":"asp-href-exclude","TypeName":"System.String","Metadata":{"Common.PropertyName":"HrefExclude"}},{"Kind":"ITagHelper","Name":"asp-href-include","TypeName":"System.String","Metadata":{"Common.PropertyName":"HrefInclude"}},{"Kind":"ITagHelper","Name":"asp-suppress-fallback-integrity","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"SuppressFallbackIntegrity"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper","Common.TypeNameIdentifier":"LinkTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"option"}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"value","TypeName":"System.String","Metadata":{"Common.PropertyName":"Value"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper","Common.TypeNameIdentifier":"OptionTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"partial","TagStructure":2,"Attributes":[{"Name":"name"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"fallback-name","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackName"}},{"Kind":"ITagHelper","Name":"for","TypeName":"Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression","Metadata":{"Common.PropertyName":"For"}},{"Kind":"ITagHelper","Name":"model","TypeName":"System.Object","Metadata":{"Common.PropertyName":"Model"}},{"Kind":"ITagHelper","Name":"name","TypeName":"System.String","Metadata":{"Common.PropertyName":"Name"}},{"Kind":"ITagHelper","Name":"optional","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"Optional"}},{"Kind":"ITagHelper","Name":"view-data","TypeName":"Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary","IndexerNamePrefix":"view-data-","IndexerTypeName":"System.Object","Metadata":{"Common.PropertyName":"ViewData"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper","Common.TypeNameIdentifier":"PartialTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"form"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper","Common.TypeNameIdentifier":"RenderAtEndOfFormTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"script","Attributes":[{"Name":"asp-append-version"}]},{"TagName":"script","Attributes":[{"Name":"asp-fallback-src"}]},{"TagName":"script","Attributes":[{"Name":"asp-fallback-src-exclude"}]},{"TagName":"script","Attributes":[{"Name":"asp-fallback-src-include"}]},{"TagName":"script","Attributes":[{"Name":"asp-fallback-test"}]},{"TagName":"script","Attributes":[{"Name":"asp-src-exclude"}]},{"TagName":"script","Attributes":[{"Name":"asp-src-include"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-append-version","TypeName":"System.Boolean?","Metadata":{"Common.PropertyName":"AppendVersion"}},{"Kind":"ITagHelper","Name":"asp-fallback-src","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackSrc"}},{"Kind":"ITagHelper","Name":"asp-fallback-src-exclude","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackSrcExclude"}},{"Kind":"ITagHelper","Name":"asp-fallback-src-include","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackSrcInclude"}},{"Kind":"ITagHelper","Name":"asp-fallback-test","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackTestExpression"}},{"Kind":"ITagHelper","Name":"src","TypeName":"System.String","Metadata":{"Common.PropertyName":"Src"}},{"Kind":"ITagHelper","Name":"asp-src-exclude","TypeName":"System.String","Metadata":{"Common.PropertyName":"SrcExclude"}},{"Kind":"ITagHelper","Name":"asp-src-include","TypeName":"System.String","Metadata":{"Common.PropertyName":"SrcInclude"}},{"Kind":"ITagHelper","Name":"asp-suppress-fallback-integrity","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"SuppressFallbackIntegrity"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper","Common.TypeNameIdentifier":"ScriptTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"select","Attributes":[{"Name":"asp-for"}]},{"TagName":"select","Attributes":[{"Name":"asp-items"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-for","TypeName":"Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression","Metadata":{"Common.PropertyName":"For"}},{"Kind":"ITagHelper","Name":"asp-items","TypeName":"System.Collections.Generic.IEnumerable<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>","Metadata":{"Common.PropertyName":"Items"}},{"Kind":"ITagHelper","Name":"name","TypeName":"System.String","Metadata":{"Common.PropertyName":"Name"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper","Common.TypeNameIdentifier":"SelectTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.TextAreaTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"textarea","Attributes":[{"Name":"asp-for"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-for","TypeName":"Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression","Metadata":{"Common.PropertyName":"For"}},{"Kind":"ITagHelper","Name":"name","TypeName":"System.String","Metadata":{"Common.PropertyName":"Name"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.TextAreaTagHelper","Common.TypeNameIdentifier":"TextAreaTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.ValidationMessageTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"span","Attributes":[{"Name":"asp-validation-for"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-validation-for","TypeName":"Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression","Metadata":{"Common.PropertyName":"For"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.ValidationMessageTagHelper","Common.TypeNameIdentifier":"ValidationMessageTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.ValidationSummaryTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"div","Attributes":[{"Name":"asp-validation-summary"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-validation-summary","TypeName":"Microsoft.AspNetCore.Mvc.Rendering.ValidationSummary","IsEnum":true,"Metadata":{"Common.PropertyName":"ValidationSummary"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.ValidationSummaryTagHelper","Common.TypeNameIdentifier":"ValidationSummaryTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"StackExchange.Profiling.MiniProfilerScriptTagHelper","AssemblyName":"MiniProfiler.AspNetCore.Mvc","CaseSensitive":false,"TagMatchingRules":[{"TagName":"mini-profiler","TagStructure":2}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"view-context","TypeName":"Microsoft.AspNetCore.Mvc.Rendering.ViewContext","Metadata":{"Common.PropertyName":"ViewContext"}},{"Kind":"ITagHelper","Name":"position","TypeName":"StackExchange.Profiling.RenderPosition?","Metadata":{"Common.PropertyName":"Position"}},{"Kind":"ITagHelper","Name":"show-trivial","TypeName":"System.Boolean?","Metadata":{"Common.PropertyName":"ShowTrivial"}},{"Kind":"ITagHelper","Name":"show-time-with-children","TypeName":"System.Boolean?","Metadata":{"Common.PropertyName":"ShowTimeWithChildren"}},{"Kind":"ITagHelper","Name":"max-traces","TypeName":"System.Int32?","Metadata":{"Common.PropertyName":"MaxTraces"}},{"Kind":"ITagHelper","Name":"show-controls","TypeName":"System.Boolean?","Metadata":{"Common.PropertyName":"ShowControls"}},{"Kind":"ITagHelper","Name":"start-hidden","TypeName":"System.Boolean?","Metadata":{"Common.PropertyName":"StartHidden"}},{"Kind":"ITagHelper","Name":"color-scheme","TypeName":"StackExchange.Profiling.ColorScheme?","Metadata":{"Common.PropertyName":"ColorScheme"}},{"Kind":"ITagHelper","Name":"nonce","TypeName":"System.String","Metadata":{"Common.PropertyName":"Nonce"}},{"Kind":"ITagHelper","Name":"options","TypeName":"StackExchange.Profiling.RenderOptions","Metadata":{"Common.PropertyName":"RenderOptions"}}],"Metadata":{"Common.TypeName":"StackExchange.Profiling.MiniProfilerScriptTagHelper","Common.TypeNameIdentifier":"MiniProfilerScriptTagHelper","Common.TypeNamespace":"StackExchange.Profiling","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"StackExchange.Profiling.ProfileScriptTagHelper","AssemblyName":"MiniProfiler.AspNetCore.Mvc","CaseSensitive":false,"TagMatchingRules":[{"TagName":"profile-script","TagStructure":1}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"name","TypeName":"System.String","Metadata":{"Common.PropertyName":"Name"}}],"Metadata":{"Common.TypeName":"StackExchange.Profiling.ProfileScriptTagHelper","Common.TypeNameIdentifier":"ProfileScriptTagHelper","Common.TypeNamespace":"StackExchange.Profiling","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"StackExchange.Profiling.ProfileTagHelper","AssemblyName":"MiniProfiler.AspNetCore.Mvc","CaseSensitive":false,"TagMatchingRules":[{"TagName":"profile","TagStructure":1}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"name","TypeName":"System.String","Metadata":{"Common.PropertyName":"Name"}}],"Metadata":{"Common.TypeName":"StackExchange.Profiling.ProfileTagHelper","Common.TypeNameIdentifier":"ProfileTagHelper","Common.TypeNamespace":"StackExchange.Profiling","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.Razor","CaseSensitive":false,"TagMatchingRules":[{"TagName":"body"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper","Common.TypeNameIdentifier":"BodyTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.Razor","CaseSensitive":false,"TagMatchingRules":[{"TagName":"head"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper","Common.TypeNameIdentifier":"HeadTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.Razor","CaseSensitive":false,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"itemid","Value":"~/","ValueComparison":2}]},{"TagName":"a","Attributes":[{"Name":"href","Value":"~/","ValueComparison":2}]},{"TagName":"applet","Attributes":[{"Name":"archive","Value":"~/","ValueComparison":2}]},{"TagName":"area","TagStructure":2,"Attributes":[{"Name":"href","Value":"~/","ValueComparison":2}]},{"TagName":"audio","Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"base","TagStructure":2,"Attributes":[{"Name":"href","Value":"~/","ValueComparison":2}]},{"TagName":"blockquote","Attributes":[{"Name":"cite","Value":"~/","ValueComparison":2}]},{"TagName":"button","Attributes":[{"Name":"formaction","Value":"~/","ValueComparison":2}]},{"TagName":"del","Attributes":[{"Name":"cite","Value":"~/","ValueComparison":2}]},{"TagName":"embed","TagStructure":2,"Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"form","Attributes":[{"Name":"action","Value":"~/","ValueComparison":2}]},{"TagName":"html","Attributes":[{"Name":"manifest","Value":"~/","ValueComparison":2}]},{"TagName":"iframe","Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"img","TagStructure":2,"Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"img","TagStructure":2,"Attributes":[{"Name":"srcset","Value":"~/","ValueComparison":2}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"formaction","Value":"~/","ValueComparison":2}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"ins","Attributes":[{"Name":"cite","Value":"~/","ValueComparison":2}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"href","Value":"~/","ValueComparison":2}]},{"TagName":"menuitem","Attributes":[{"Name":"icon","Value":"~/","ValueComparison":2}]},{"TagName":"object","Attributes":[{"Name":"archive","Value":"~/","ValueComparison":2}]},{"TagName":"object","Attributes":[{"Name":"data","Value":"~/","ValueComparison":2}]},{"TagName":"q","Attributes":[{"Name":"cite","Value":"~/","ValueComparison":2}]},{"TagName":"script","Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"source","TagStructure":2,"Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"source","TagStructure":2,"Attributes":[{"Name":"srcset","Value":"~/","ValueComparison":2}]},{"TagName":"track","TagStructure":2,"Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]},{"TagName":"video","Attributes":[{"Name":"poster","Value":"~/","ValueComparison":2}]},{"TagName":"video","Attributes":[{"Name":"src","Value":"~/","ValueComparison":2}]}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper","Common.TypeNameIdentifier":"UrlResolutionTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.Razor.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"a","Attributes":[{"Name":"asp-action"}]},{"TagName":"a","Attributes":[{"Name":"asp-all-route-data"}]},{"TagName":"a","Attributes":[{"Name":"asp-area"}]},{"TagName":"a","Attributes":[{"Name":"asp-controller"}]},{"TagName":"a","Attributes":[{"Name":"asp-fragment"}]},{"TagName":"a","Attributes":[{"Name":"asp-host"}]},{"TagName":"a","Attributes":[{"Name":"asp-page"}]},{"TagName":"a","Attributes":[{"Name":"asp-page-handler"}]},{"TagName":"a","Attributes":[{"Name":"asp-protocol"}]},{"TagName":"a","Attributes":[{"Name":"asp-route"}]},{"TagName":"a","Attributes":[{"Name":"asp-route-","NameComparison":1}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-action","TypeName":"System.String","Metadata":{"Common.PropertyName":"Action"}},{"Kind":"ITagHelper","Name":"asp-area","TypeName":"System.String","Metadata":{"Common.PropertyName":"Area"}},{"Kind":"ITagHelper","Name":"asp-controller","TypeName":"System.String","Metadata":{"Common.PropertyName":"Controller"}},{"Kind":"ITagHelper","Name":"asp-fragment","TypeName":"System.String","Metadata":{"Common.PropertyName":"Fragment"}},{"Kind":"ITagHelper","Name":"asp-host","TypeName":"System.String","Metadata":{"Common.PropertyName":"Host"}},{"Kind":"ITagHelper","Name":"asp-page","TypeName":"System.String","Metadata":{"Common.PropertyName":"Page"}},{"Kind":"ITagHelper","Name":"asp-page-handler","TypeName":"System.String","Metadata":{"Common.PropertyName":"PageHandler"}},{"Kind":"ITagHelper","Name":"asp-protocol","TypeName":"System.String","Metadata":{"Common.PropertyName":"Protocol"}},{"Kind":"ITagHelper","Name":"asp-route","TypeName":"System.String","Metadata":{"Common.PropertyName":"Route"}},{"Kind":"ITagHelper","Name":"asp-all-route-data","TypeName":"System.Collections.Generic.IDictionary<System.String, System.String>","IndexerNamePrefix":"asp-route-","IndexerTypeName":"System.String","Metadata":{"Common.PropertyName":"RouteValues"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper","Common.TypeNameIdentifier":"AnchorTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"cache"}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"priority","TypeName":"Microsoft.Extensions.Caching.Memory.CacheItemPriority?","Metadata":{"Common.PropertyName":"Priority"}},{"Kind":"ITagHelper","Name":"enabled","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"Enabled"}},{"Kind":"ITagHelper","Name":"expires-after","TypeName":"System.TimeSpan?","Metadata":{"Common.PropertyName":"ExpiresAfter"}},{"Kind":"ITagHelper","Name":"expires-on","TypeName":"System.DateTimeOffset?","Metadata":{"Common.PropertyName":"ExpiresOn"}},{"Kind":"ITagHelper","Name":"expires-sliding","TypeName":"System.TimeSpan?","Metadata":{"Common.PropertyName":"ExpiresSliding"}},{"Kind":"ITagHelper","Name":"vary-by","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryBy"}},{"Kind":"ITagHelper","Name":"vary-by-cookie","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByCookie"}},{"Kind":"ITagHelper","Name":"vary-by-culture","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"VaryByCulture"}},{"Kind":"ITagHelper","Name":"vary-by-header","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByHeader"}},{"Kind":"ITagHelper","Name":"vary-by-query","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByQuery"}},{"Kind":"ITagHelper","Name":"vary-by-route","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByRoute"}},{"Kind":"ITagHelper","Name":"vary-by-user","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"VaryByUser"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelper","Common.TypeNameIdentifier":"CacheTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.ComponentTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"component","TagStructure":2,"Attributes":[{"Name":"type"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"type","TypeName":"System.Type","Metadata":{"Common.PropertyName":"ComponentType"}},{"Kind":"ITagHelper","Name":"params","TypeName":"System.Collections.Generic.IDictionary<System.String, System.Object>","IndexerNamePrefix":"param-","IndexerTypeName":"System.Object","Metadata":{"Common.PropertyName":"Parameters"}},{"Kind":"ITagHelper","Name":"render-mode","TypeName":"Microsoft.AspNetCore.Mvc.Rendering.RenderMode","IsEnum":true,"Metadata":{"Common.PropertyName":"RenderMode"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.ComponentTagHelper","Common.TypeNameIdentifier":"ComponentTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.DistributedCacheTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"distributed-cache","Attributes":[{"Name":"name"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"name","TypeName":"System.String","Metadata":{"Common.PropertyName":"Name"}},{"Kind":"ITagHelper","Name":"enabled","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"Enabled"}},{"Kind":"ITagHelper","Name":"expires-after","TypeName":"System.TimeSpan?","Metadata":{"Common.PropertyName":"ExpiresAfter"}},{"Kind":"ITagHelper","Name":"expires-on","TypeName":"System.DateTimeOffset?","Metadata":{"Common.PropertyName":"ExpiresOn"}},{"Kind":"ITagHelper","Name":"expires-sliding","TypeName":"System.TimeSpan?","Metadata":{"Common.PropertyName":"ExpiresSliding"}},{"Kind":"ITagHelper","Name":"vary-by","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryBy"}},{"Kind":"ITagHelper","Name":"vary-by-cookie","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByCookie"}},{"Kind":"ITagHelper","Name":"vary-by-culture","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"VaryByCulture"}},{"Kind":"ITagHelper","Name":"vary-by-header","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByHeader"}},{"Kind":"ITagHelper","Name":"vary-by-query","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByQuery"}},{"Kind":"ITagHelper","Name":"vary-by-route","TypeName":"System.String","Metadata":{"Common.PropertyName":"VaryByRoute"}},{"Kind":"ITagHelper","Name":"vary-by-user","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"VaryByUser"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.DistributedCacheTagHelper","Common.TypeNameIdentifier":"DistributedCacheTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"environment"}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"exclude","TypeName":"System.String","Metadata":{"Common.PropertyName":"Exclude"}},{"Kind":"ITagHelper","Name":"include","TypeName":"System.String","Metadata":{"Common.PropertyName":"Include"}},{"Kind":"ITagHelper","Name":"names","TypeName":"System.String","Metadata":{"Common.PropertyName":"Names"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper","Common.TypeNameIdentifier":"EnvironmentTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"button","Attributes":[{"Name":"asp-action"}]},{"TagName":"button","Attributes":[{"Name":"asp-all-route-data"}]},{"TagName":"button","Attributes":[{"Name":"asp-area"}]},{"TagName":"button","Attributes":[{"Name":"asp-controller"}]},{"TagName":"button","Attributes":[{"Name":"asp-fragment"}]},{"TagName":"button","Attributes":[{"Name":"asp-page"}]},{"TagName":"button","Attributes":[{"Name":"asp-page-handler"}]},{"TagName":"button","Attributes":[{"Name":"asp-route"}]},{"TagName":"button","Attributes":[{"Name":"asp-route-","NameComparison":1}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-action"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-all-route-data"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-area"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-controller"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-fragment"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-page"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-page-handler"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-route"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"image","ValueComparison":1},{"Name":"asp-route-","NameComparison":1}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-action"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-all-route-data"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-area"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-controller"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-fragment"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-page"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-page-handler"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-route"}]},{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"type","Value":"submit","ValueComparison":1},{"Name":"asp-route-","NameComparison":1}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-action","TypeName":"System.String","Metadata":{"Common.PropertyName":"Action"}},{"Kind":"ITagHelper","Name":"asp-area","TypeName":"System.String","Metadata":{"Common.PropertyName":"Area"}},{"Kind":"ITagHelper","Name":"asp-controller","TypeName":"System.String","Metadata":{"Common.PropertyName":"Controller"}},{"Kind":"ITagHelper","Name":"asp-fragment","TypeName":"System.String","Metadata":{"Common.PropertyName":"Fragment"}},{"Kind":"ITagHelper","Name":"asp-page","TypeName":"System.String","Metadata":{"Common.PropertyName":"Page"}},{"Kind":"ITagHelper","Name":"asp-page-handler","TypeName":"System.String","Metadata":{"Common.PropertyName":"PageHandler"}},{"Kind":"ITagHelper","Name":"asp-route","TypeName":"System.String","Metadata":{"Common.PropertyName":"Route"}},{"Kind":"ITagHelper","Name":"asp-all-route-data","TypeName":"System.Collections.Generic.IDictionary<System.String, System.String>","IndexerNamePrefix":"asp-route-","IndexerTypeName":"System.String","Metadata":{"Common.PropertyName":"RouteValues"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper","Common.TypeNameIdentifier":"FormActionTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"form"}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-action","TypeName":"System.String","Metadata":{"Common.PropertyName":"Action"}},{"Kind":"ITagHelper","Name":"asp-antiforgery","TypeName":"System.Boolean?","Metadata":{"Common.PropertyName":"Antiforgery"}},{"Kind":"ITagHelper","Name":"asp-area","TypeName":"System.String","Metadata":{"Common.PropertyName":"Area"}},{"Kind":"ITagHelper","Name":"asp-controller","TypeName":"System.String","Metadata":{"Common.PropertyName":"Controller"}},{"Kind":"ITagHelper","Name":"asp-fragment","TypeName":"System.String","Metadata":{"Common.PropertyName":"Fragment"}},{"Kind":"ITagHelper","Name":"method","TypeName":"System.String","Metadata":{"Common.PropertyName":"Method"}},{"Kind":"ITagHelper","Name":"asp-page","TypeName":"System.String","Metadata":{"Common.PropertyName":"Page"}},{"Kind":"ITagHelper","Name":"asp-page-handler","TypeName":"System.String","Metadata":{"Common.PropertyName":"PageHandler"}},{"Kind":"ITagHelper","Name":"asp-route","TypeName":"System.String","Metadata":{"Common.PropertyName":"Route"}},{"Kind":"ITagHelper","Name":"asp-all-route-data","TypeName":"System.Collections.Generic.IDictionary<System.String, System.String>","IndexerNamePrefix":"asp-route-","IndexerTypeName":"System.String","Metadata":{"Common.PropertyName":"RouteValues"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper","Common.TypeNameIdentifier":"FormTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.ImageTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"img","TagStructure":2,"Attributes":[{"Name":"asp-append-version"},{"Name":"src"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-append-version","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"AppendVersion"}},{"Kind":"ITagHelper","Name":"src","TypeName":"System.String","Metadata":{"Common.PropertyName":"Src"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.ImageTagHelper","Common.TypeNameIdentifier":"ImageTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"input","TagStructure":2,"Attributes":[{"Name":"asp-for"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-for","TypeName":"Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression","Metadata":{"Common.PropertyName":"For"}},{"Kind":"ITagHelper","Name":"asp-format","TypeName":"System.String","Metadata":{"Common.PropertyName":"Format"}},{"Kind":"ITagHelper","Name":"type","TypeName":"System.String","Metadata":{"Common.PropertyName":"InputTypeName"}},{"Kind":"ITagHelper","Name":"name","TypeName":"System.String","Metadata":{"Common.PropertyName":"Name"}},{"Kind":"ITagHelper","Name":"value","TypeName":"System.String","Metadata":{"Common.PropertyName":"Value"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper","Common.TypeNameIdentifier":"InputTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.LabelTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"label","Attributes":[{"Name":"asp-for"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-for","TypeName":"Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression","Metadata":{"Common.PropertyName":"For"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.LabelTagHelper","Common.TypeNameIdentifier":"LabelTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-append-version"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-fallback-href"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-fallback-href-exclude"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-fallback-href-include"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-fallback-test-class"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-fallback-test-property"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-fallback-test-value"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-href-exclude"}]},{"TagName":"link","TagStructure":2,"Attributes":[{"Name":"asp-href-include"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-append-version","TypeName":"System.Boolean?","Metadata":{"Common.PropertyName":"AppendVersion"}},{"Kind":"ITagHelper","Name":"asp-fallback-href","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackHref"}},{"Kind":"ITagHelper","Name":"asp-fallback-href-exclude","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackHrefExclude"}},{"Kind":"ITagHelper","Name":"asp-fallback-href-include","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackHrefInclude"}},{"Kind":"ITagHelper","Name":"asp-fallback-test-class","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackTestClass"}},{"Kind":"ITagHelper","Name":"asp-fallback-test-property","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackTestProperty"}},{"Kind":"ITagHelper","Name":"asp-fallback-test-value","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackTestValue"}},{"Kind":"ITagHelper","Name":"href","TypeName":"System.String","Metadata":{"Common.PropertyName":"Href"}},{"Kind":"ITagHelper","Name":"asp-href-exclude","TypeName":"System.String","Metadata":{"Common.PropertyName":"HrefExclude"}},{"Kind":"ITagHelper","Name":"asp-href-include","TypeName":"System.String","Metadata":{"Common.PropertyName":"HrefInclude"}},{"Kind":"ITagHelper","Name":"asp-suppress-fallback-integrity","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"SuppressFallbackIntegrity"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper","Common.TypeNameIdentifier":"LinkTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"option"}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"value","TypeName":"System.String","Metadata":{"Common.PropertyName":"Value"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper","Common.TypeNameIdentifier":"OptionTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"partial","TagStructure":2,"Attributes":[{"Name":"name"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"fallback-name","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackName"}},{"Kind":"ITagHelper","Name":"for","TypeName":"Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression","Metadata":{"Common.PropertyName":"For"}},{"Kind":"ITagHelper","Name":"model","TypeName":"System.Object","Metadata":{"Common.PropertyName":"Model"}},{"Kind":"ITagHelper","Name":"name","TypeName":"System.String","Metadata":{"Common.PropertyName":"Name"}},{"Kind":"ITagHelper","Name":"optional","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"Optional"}},{"Kind":"ITagHelper","Name":"view-data","TypeName":"Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary","IndexerNamePrefix":"view-data-","IndexerTypeName":"System.Object","Metadata":{"Common.PropertyName":"ViewData"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper","Common.TypeNameIdentifier":"PartialTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"form"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper","Common.TypeNameIdentifier":"RenderAtEndOfFormTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"script","Attributes":[{"Name":"asp-append-version"}]},{"TagName":"script","Attributes":[{"Name":"asp-fallback-src"}]},{"TagName":"script","Attributes":[{"Name":"asp-fallback-src-exclude"}]},{"TagName":"script","Attributes":[{"Name":"asp-fallback-src-include"}]},{"TagName":"script","Attributes":[{"Name":"asp-fallback-test"}]},{"TagName":"script","Attributes":[{"Name":"asp-src-exclude"}]},{"TagName":"script","Attributes":[{"Name":"asp-src-include"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-append-version","TypeName":"System.Boolean?","Metadata":{"Common.PropertyName":"AppendVersion"}},{"Kind":"ITagHelper","Name":"asp-fallback-src","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackSrc"}},{"Kind":"ITagHelper","Name":"asp-fallback-src-exclude","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackSrcExclude"}},{"Kind":"ITagHelper","Name":"asp-fallback-src-include","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackSrcInclude"}},{"Kind":"ITagHelper","Name":"asp-fallback-test","TypeName":"System.String","Metadata":{"Common.PropertyName":"FallbackTestExpression"}},{"Kind":"ITagHelper","Name":"src","TypeName":"System.String","Metadata":{"Common.PropertyName":"Src"}},{"Kind":"ITagHelper","Name":"asp-src-exclude","TypeName":"System.String","Metadata":{"Common.PropertyName":"SrcExclude"}},{"Kind":"ITagHelper","Name":"asp-src-include","TypeName":"System.String","Metadata":{"Common.PropertyName":"SrcInclude"}},{"Kind":"ITagHelper","Name":"asp-suppress-fallback-integrity","TypeName":"System.Boolean","Metadata":{"Common.PropertyName":"SuppressFallbackIntegrity"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper","Common.TypeNameIdentifier":"ScriptTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"select","Attributes":[{"Name":"asp-for"}]},{"TagName":"select","Attributes":[{"Name":"asp-items"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-for","TypeName":"Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression","Metadata":{"Common.PropertyName":"For"}},{"Kind":"ITagHelper","Name":"asp-items","TypeName":"System.Collections.Generic.IEnumerable<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>","Metadata":{"Common.PropertyName":"Items"}},{"Kind":"ITagHelper","Name":"name","TypeName":"System.String","Metadata":{"Common.PropertyName":"Name"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper","Common.TypeNameIdentifier":"SelectTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.TextAreaTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"textarea","Attributes":[{"Name":"asp-for"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-for","TypeName":"Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression","Metadata":{"Common.PropertyName":"For"}},{"Kind":"ITagHelper","Name":"name","TypeName":"System.String","Metadata":{"Common.PropertyName":"Name"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.TextAreaTagHelper","Common.TypeNameIdentifier":"TextAreaTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.ValidationMessageTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"span","Attributes":[{"Name":"asp-validation-for"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-validation-for","TypeName":"Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression","Metadata":{"Common.PropertyName":"For"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.ValidationMessageTagHelper","Common.TypeNameIdentifier":"ValidationMessageTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"Microsoft.AspNetCore.Mvc.TagHelpers.ValidationSummaryTagHelper","AssemblyName":"Microsoft.AspNetCore.Mvc.TagHelpers","CaseSensitive":false,"TagMatchingRules":[{"TagName":"div","Attributes":[{"Name":"asp-validation-summary"}]}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"asp-validation-summary","TypeName":"Microsoft.AspNetCore.Mvc.Rendering.ValidationSummary","IsEnum":true,"Metadata":{"Common.PropertyName":"ValidationSummary"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Mvc.TagHelpers.ValidationSummaryTagHelper","Common.TypeNameIdentifier":"ValidationSummaryTagHelper","Common.TypeNamespace":"Microsoft.AspNetCore.Mvc.TagHelpers","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"StackExchange.Profiling.MiniProfilerScriptTagHelper","AssemblyName":"MiniProfiler.AspNetCore.Mvc","CaseSensitive":false,"TagMatchingRules":[{"TagName":"mini-profiler","TagStructure":2}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"view-context","TypeName":"Microsoft.AspNetCore.Mvc.Rendering.ViewContext","Metadata":{"Common.PropertyName":"ViewContext"}},{"Kind":"ITagHelper","Name":"position","TypeName":"StackExchange.Profiling.RenderPosition?","Metadata":{"Common.PropertyName":"Position"}},{"Kind":"ITagHelper","Name":"show-trivial","TypeName":"System.Boolean?","Metadata":{"Common.PropertyName":"ShowTrivial"}},{"Kind":"ITagHelper","Name":"show-time-with-children","TypeName":"System.Boolean?","Metadata":{"Common.PropertyName":"ShowTimeWithChildren"}},{"Kind":"ITagHelper","Name":"max-traces","TypeName":"System.Int32?","Metadata":{"Common.PropertyName":"MaxTraces"}},{"Kind":"ITagHelper","Name":"show-controls","TypeName":"System.Boolean?","Metadata":{"Common.PropertyName":"ShowControls"}},{"Kind":"ITagHelper","Name":"start-hidden","TypeName":"System.Boolean?","Metadata":{"Common.PropertyName":"StartHidden"}},{"Kind":"ITagHelper","Name":"color-scheme","TypeName":"StackExchange.Profiling.ColorScheme?","Metadata":{"Common.PropertyName":"ColorScheme"}},{"Kind":"ITagHelper","Name":"nonce","TypeName":"System.String","Metadata":{"Common.PropertyName":"Nonce"}},{"Kind":"ITagHelper","Name":"options","TypeName":"StackExchange.Profiling.RenderOptions","Metadata":{"Common.PropertyName":"RenderOptions"}}],"Metadata":{"Common.TypeName":"StackExchange.Profiling.MiniProfilerScriptTagHelper","Common.TypeNameIdentifier":"MiniProfilerScriptTagHelper","Common.TypeNamespace":"StackExchange.Profiling","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"StackExchange.Profiling.ProfileScriptTagHelper","AssemblyName":"MiniProfiler.AspNetCore.Mvc","CaseSensitive":false,"TagMatchingRules":[{"TagName":"profile-script","TagStructure":1}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"name","TypeName":"System.String","Metadata":{"Common.PropertyName":"Name"}}],"Metadata":{"Common.TypeName":"StackExchange.Profiling.ProfileScriptTagHelper","Common.TypeNameIdentifier":"ProfileScriptTagHelper","Common.TypeNamespace":"StackExchange.Profiling","Runtime.Name":"ITagHelper"}},{"Kind":"ITagHelper","Name":"StackExchange.Profiling.ProfileTagHelper","AssemblyName":"MiniProfiler.AspNetCore.Mvc","CaseSensitive":false,"TagMatchingRules":[{"TagName":"profile","TagStructure":1}],"BoundAttributes":[{"Kind":"ITagHelper","Name":"name","TypeName":"System.String","Metadata":{"Common.PropertyName":"Name"}}],"Metadata":{"Common.TypeName":"StackExchange.Profiling.ProfileTagHelper","Common.TypeNameIdentifier":"ProfileTagHelper","Common.TypeNamespace":"StackExchange.Profiling","Runtime.Name":"ITagHelper"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"AuthorizeRouteView"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"Authorizing","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"Authorizing","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"NotAuthorized","TypeName":"Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment<global::Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Common.PropertyName":"NotAuthorized","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"DefaultLayout","TypeName":"System.Type","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Type","Common.PropertyName":"DefaultLayout"}},{"Kind":"Components.Component","Name":"RouteData","TypeName":"Microsoft.AspNetCore.Components.RouteData","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RouteData","Common.PropertyName":"RouteData"}},{"Kind":"Components.Component","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for all child content expressions.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView","Common.TypeNameIdentifier":"AuthorizeRouteView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"Authorizing","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"Authorizing","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"NotAuthorized","TypeName":"Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment<global::Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Common.PropertyName":"NotAuthorized","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"DefaultLayout","TypeName":"System.Type","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Type","Common.PropertyName":"DefaultLayout"}},{"Kind":"Components.Component","Name":"RouteData","TypeName":"Microsoft.AspNetCore.Components.RouteData","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RouteData","Common.PropertyName":"RouteData"}},{"Kind":"Components.Component","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for all child content expressions.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView","Common.TypeNameIdentifier":"AuthorizeRouteView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.Authorizing","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Authorizing","ParentTag":"AuthorizeRouteView"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.Authorizing","Common.TypeNameIdentifier":"AuthorizeRouteView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.IsSpecialKind":"Components.ChildContent","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.Authorizing","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Authorizing","ParentTag":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.Authorizing","Common.TypeNameIdentifier":"AuthorizeRouteView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.IsSpecialKind":"Components.ChildContent","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.NotAuthorized","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"NotAuthorized","ParentTag":"AuthorizeRouteView"}],"BoundAttributes":[{"Kind":"Components.ChildContent","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for the 'NotAuthorized' child content expression.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.NotAuthorized","Common.TypeNameIdentifier":"AuthorizeRouteView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.IsSpecialKind":"Components.ChildContent","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.NotAuthorized","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"NotAuthorized","ParentTag":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView"}],"BoundAttributes":[{"Kind":"Components.ChildContent","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for the 'NotAuthorized' child content expression.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.NotAuthorized","Common.TypeNameIdentifier":"AuthorizeRouteView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.IsSpecialKind":"Components.ChildContent","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"AuthorizeView"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"Policy","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"Policy"}},{"Kind":"Components.Component","Name":"Roles","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"Roles"}},{"Kind":"Components.Component","Name":"Authorized","TypeName":"Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment<global::Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Common.PropertyName":"Authorized","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"Authorizing","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"Authorizing","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"ChildContent","TypeName":"Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment<global::Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Common.PropertyName":"ChildContent","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"NotAuthorized","TypeName":"Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment<global::Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Common.PropertyName":"NotAuthorized","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"Resource","TypeName":"System.Object","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Object","Common.PropertyName":"Resource"}},{"Kind":"Components.Component","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for all child content expressions.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView","Common.TypeNameIdentifier":"AuthorizeView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"Policy","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"Policy"}},{"Kind":"Components.Component","Name":"Roles","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"Roles"}},{"Kind":"Components.Component","Name":"Authorized","TypeName":"Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment<global::Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Common.PropertyName":"Authorized","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"Authorizing","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"Authorizing","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"ChildContent","TypeName":"Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment<global::Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Common.PropertyName":"ChildContent","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"NotAuthorized","TypeName":"Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment<global::Microsoft.AspNetCore.Components.Authorization.AuthenticationState>","Common.PropertyName":"NotAuthorized","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"Resource","TypeName":"System.Object","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Object","Common.PropertyName":"Resource"}},{"Kind":"Components.Component","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for all child content expressions.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView","Common.TypeNameIdentifier":"AuthorizeView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorized","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Authorized","ParentTag":"AuthorizeView"}],"BoundAttributes":[{"Kind":"Components.ChildContent","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for the 'Authorized' child content expression.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorized","Common.TypeNameIdentifier":"AuthorizeView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.IsSpecialKind":"Components.ChildContent","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorized","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Authorized","ParentTag":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView"}],"BoundAttributes":[{"Kind":"Components.ChildContent","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for the 'Authorized' child content expression.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorized","Common.TypeNameIdentifier":"AuthorizeView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.IsSpecialKind":"Components.ChildContent","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorizing","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Authorizing","ParentTag":"AuthorizeView"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorizing","Common.TypeNameIdentifier":"AuthorizeView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.IsSpecialKind":"Components.ChildContent","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorizing","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Authorizing","ParentTag":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorizing","Common.TypeNameIdentifier":"AuthorizeView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.IsSpecialKind":"Components.ChildContent","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.ChildContent","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ChildContent","ParentTag":"AuthorizeView"}],"BoundAttributes":[{"Kind":"Components.ChildContent","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for the 'ChildContent' child content expression.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.ChildContent","Common.TypeNameIdentifier":"AuthorizeView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.IsSpecialKind":"Components.ChildContent","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.ChildContent","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ChildContent","ParentTag":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView"}],"BoundAttributes":[{"Kind":"Components.ChildContent","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for the 'ChildContent' child content expression.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.ChildContent","Common.TypeNameIdentifier":"AuthorizeView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.IsSpecialKind":"Components.ChildContent","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.NotAuthorized","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"NotAuthorized","ParentTag":"AuthorizeView"}],"BoundAttributes":[{"Kind":"Components.ChildContent","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for the 'NotAuthorized' child content expression.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.NotAuthorized","Common.TypeNameIdentifier":"AuthorizeView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.IsSpecialKind":"Components.ChildContent","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.NotAuthorized","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"NotAuthorized","ParentTag":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView"}],"BoundAttributes":[{"Kind":"Components.ChildContent","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for the 'NotAuthorized' child content expression.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.AuthorizeView.NotAuthorized","Common.TypeNameIdentifier":"AuthorizeView","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.IsSpecialKind":"Components.ChildContent","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"CascadingAuthenticationState"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"ChildContent","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"ChildContent","Components.ChildContent":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState","Common.TypeNameIdentifier":"CascadingAuthenticationState","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"ChildContent","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"ChildContent","Components.ChildContent":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState","Common.TypeNameIdentifier":"CascadingAuthenticationState","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState.ChildContent","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ChildContent","ParentTag":"CascadingAuthenticationState"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState.ChildContent","Common.TypeNameIdentifier":"CascadingAuthenticationState","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.IsSpecialKind":"Components.ChildContent","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState.ChildContent","AssemblyName":"Microsoft.AspNetCore.Components.Authorization","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ChildContent","ParentTag":"Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState.ChildContent","Common.TypeNameIdentifier":"CascadingAuthenticationState","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Authorization","Components.IsSpecialKind":"Components.ChildContent","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.CascadingValue<TValue>","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"CascadingValue"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"TValue","TypeName":"System.Type","Documentation":"Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.CascadingValue<TValue> component.","Metadata":{"Common.PropertyName":"TValue","Components.TypeParameter":"True","Components.TypeParameterIsCascading":"False"}},{"Kind":"Components.Component","Name":"ChildContent","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"ChildContent","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"IsFixed","TypeName":"System.Boolean","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Boolean","Common.PropertyName":"IsFixed"}},{"Kind":"Components.Component","Name":"Name","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"Name"}},{"Kind":"Components.Component","Name":"Value","TypeName":"TValue","Metadata":{"Common.GloballyQualifiedTypeName":"TValue","Common.PropertyName":"Value","Components.GenericTyped":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.CascadingValue<TValue>","Common.TypeNameIdentifier":"CascadingValue","Common.TypeNamespace":"Microsoft.AspNetCore.Components","Components.GenericTyped":"True","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.CascadingValue<TValue>","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.CascadingValue"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"TValue","TypeName":"System.Type","Documentation":"Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.CascadingValue<TValue> component.","Metadata":{"Common.PropertyName":"TValue","Components.TypeParameter":"True","Components.TypeParameterIsCascading":"False"}},{"Kind":"Components.Component","Name":"ChildContent","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"ChildContent","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"IsFixed","TypeName":"System.Boolean","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Boolean","Common.PropertyName":"IsFixed"}},{"Kind":"Components.Component","Name":"Name","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"Name"}},{"Kind":"Components.Component","Name":"Value","TypeName":"TValue","Metadata":{"Common.GloballyQualifiedTypeName":"TValue","Common.PropertyName":"Value","Components.GenericTyped":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.CascadingValue<TValue>","Common.TypeNameIdentifier":"CascadingValue","Common.TypeNamespace":"Microsoft.AspNetCore.Components","Components.GenericTyped":"True","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.CascadingValue<TValue>.ChildContent","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ChildContent","ParentTag":"CascadingValue"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.CascadingValue<TValue>.ChildContent","Common.TypeNameIdentifier":"CascadingValue","Common.TypeNamespace":"Microsoft.AspNetCore.Components","Components.IsSpecialKind":"Components.ChildContent","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.CascadingValue<TValue>.ChildContent","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ChildContent","ParentTag":"Microsoft.AspNetCore.Components.CascadingValue"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.CascadingValue<TValue>.ChildContent","Common.TypeNameIdentifier":"CascadingValue","Common.TypeNamespace":"Microsoft.AspNetCore.Components","Components.IsSpecialKind":"Components.ChildContent","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.LayoutView","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"LayoutView"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"ChildContent","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"ChildContent","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"Layout","TypeName":"System.Type","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Type","Common.PropertyName":"Layout"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.LayoutView","Common.TypeNameIdentifier":"LayoutView","Common.TypeNamespace":"Microsoft.AspNetCore.Components","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.LayoutView","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.LayoutView"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"ChildContent","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"ChildContent","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"Layout","TypeName":"System.Type","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Type","Common.PropertyName":"Layout"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.LayoutView","Common.TypeNameIdentifier":"LayoutView","Common.TypeNamespace":"Microsoft.AspNetCore.Components","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.LayoutView.ChildContent","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ChildContent","ParentTag":"LayoutView"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.LayoutView.ChildContent","Common.TypeNameIdentifier":"LayoutView","Common.TypeNamespace":"Microsoft.AspNetCore.Components","Components.IsSpecialKind":"Components.ChildContent","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.LayoutView.ChildContent","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ChildContent","ParentTag":"Microsoft.AspNetCore.Components.LayoutView"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.LayoutView.ChildContent","Common.TypeNameIdentifier":"LayoutView","Common.TypeNamespace":"Microsoft.AspNetCore.Components","Components.IsSpecialKind":"Components.ChildContent","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.RouteView","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"RouteView"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"DefaultLayout","TypeName":"System.Type","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Type","Common.PropertyName":"DefaultLayout"}},{"Kind":"Components.Component","Name":"RouteData","TypeName":"Microsoft.AspNetCore.Components.RouteData","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RouteData","Common.PropertyName":"RouteData"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.RouteView","Common.TypeNameIdentifier":"RouteView","Common.TypeNamespace":"Microsoft.AspNetCore.Components","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.RouteView","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.RouteView"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"DefaultLayout","TypeName":"System.Type","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Type","Common.PropertyName":"DefaultLayout"}},{"Kind":"Components.Component","Name":"RouteData","TypeName":"Microsoft.AspNetCore.Components.RouteData","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RouteData","Common.PropertyName":"RouteData"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.RouteView","Common.TypeNameIdentifier":"RouteView","Common.TypeNamespace":"Microsoft.AspNetCore.Components","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Routing.Router","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Router"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"AdditionalAssemblies","TypeName":"System.Collections.Generic.IEnumerable<System.Reflection.Assembly>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IEnumerable<global::System.Reflection.Assembly>","Common.PropertyName":"AdditionalAssemblies"}},{"Kind":"Components.Component","Name":"AppAssembly","TypeName":"System.Reflection.Assembly","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Reflection.Assembly","Common.PropertyName":"AppAssembly"}},{"Kind":"Components.Component","Name":"Found","TypeName":"Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.RouteData>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment<global::Microsoft.AspNetCore.Components.RouteData>","Common.PropertyName":"Found","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"NotFound","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"NotFound","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for all child content expressions.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Routing.Router","Common.TypeNameIdentifier":"Router","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Routing","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Routing.Router","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Routing.Router"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"AdditionalAssemblies","TypeName":"System.Collections.Generic.IEnumerable<System.Reflection.Assembly>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IEnumerable<global::System.Reflection.Assembly>","Common.PropertyName":"AdditionalAssemblies"}},{"Kind":"Components.Component","Name":"AppAssembly","TypeName":"System.Reflection.Assembly","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Reflection.Assembly","Common.PropertyName":"AppAssembly"}},{"Kind":"Components.Component","Name":"Found","TypeName":"Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.RouteData>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment<global::Microsoft.AspNetCore.Components.RouteData>","Common.PropertyName":"Found","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"NotFound","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"NotFound","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for all child content expressions.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Routing.Router","Common.TypeNameIdentifier":"Router","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Routing","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Routing.Router.Found","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Found","ParentTag":"Router"}],"BoundAttributes":[{"Kind":"Components.ChildContent","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for the 'Found' child content expression.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Routing.Router.Found","Common.TypeNameIdentifier":"Router","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Routing","Components.IsSpecialKind":"Components.ChildContent","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Routing.Router.Found","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Found","ParentTag":"Microsoft.AspNetCore.Components.Routing.Router"}],"BoundAttributes":[{"Kind":"Components.ChildContent","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for the 'Found' child content expression.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Routing.Router.Found","Common.TypeNameIdentifier":"Router","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Routing","Components.IsSpecialKind":"Components.ChildContent","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Routing.Router.NotFound","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"NotFound","ParentTag":"Router"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Routing.Router.NotFound","Common.TypeNameIdentifier":"Router","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Routing","Components.IsSpecialKind":"Components.ChildContent","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Routing.Router.NotFound","AssemblyName":"Microsoft.AspNetCore.Components","CaseSensitive":true,"TagMatchingRules":[{"TagName":"NotFound","ParentTag":"Microsoft.AspNetCore.Components.Routing.Router"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Routing.Router.NotFound","Common.TypeNameIdentifier":"Router","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Routing","Components.IsSpecialKind":"Components.ChildContent","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.DataAnnotationsValidator","AssemblyName":"Microsoft.AspNetCore.Components.Forms","CaseSensitive":true,"TagMatchingRules":[{"TagName":"DataAnnotationsValidator"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.DataAnnotationsValidator","Common.TypeNameIdentifier":"DataAnnotationsValidator","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.DataAnnotationsValidator","AssemblyName":"Microsoft.AspNetCore.Components.Forms","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.DataAnnotationsValidator"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.DataAnnotationsValidator","Common.TypeNameIdentifier":"DataAnnotationsValidator","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.EditForm","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"EditForm"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"ChildContent","TypeName":"Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Forms.EditContext>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment<global::Microsoft.AspNetCore.Components.Forms.EditContext>","Common.PropertyName":"ChildContent","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"EditContext","TypeName":"Microsoft.AspNetCore.Components.Forms.EditContext","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.Forms.EditContext","Common.PropertyName":"EditContext"}},{"Kind":"Components.Component","Name":"Model","TypeName":"System.Object","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Object","Common.PropertyName":"Model"}},{"Kind":"Components.Component","Name":"OnInvalidSubmit","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Forms.EditContext>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<global::Microsoft.AspNetCore.Components.Forms.EditContext>","Common.PropertyName":"OnInvalidSubmit","Components.EventCallback":"True"}},{"Kind":"Components.Component","Name":"OnSubmit","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Forms.EditContext>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<global::Microsoft.AspNetCore.Components.Forms.EditContext>","Common.PropertyName":"OnSubmit","Components.EventCallback":"True"}},{"Kind":"Components.Component","Name":"OnValidSubmit","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Forms.EditContext>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<global::Microsoft.AspNetCore.Components.Forms.EditContext>","Common.PropertyName":"OnValidSubmit","Components.EventCallback":"True"}},{"Kind":"Components.Component","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for all child content expressions.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.EditForm","Common.TypeNameIdentifier":"EditForm","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.EditForm","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.EditForm"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"ChildContent","TypeName":"Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Forms.EditContext>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment<global::Microsoft.AspNetCore.Components.Forms.EditContext>","Common.PropertyName":"ChildContent","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"EditContext","TypeName":"Microsoft.AspNetCore.Components.Forms.EditContext","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.Forms.EditContext","Common.PropertyName":"EditContext"}},{"Kind":"Components.Component","Name":"Model","TypeName":"System.Object","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Object","Common.PropertyName":"Model"}},{"Kind":"Components.Component","Name":"OnInvalidSubmit","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Forms.EditContext>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<global::Microsoft.AspNetCore.Components.Forms.EditContext>","Common.PropertyName":"OnInvalidSubmit","Components.EventCallback":"True"}},{"Kind":"Components.Component","Name":"OnSubmit","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Forms.EditContext>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<global::Microsoft.AspNetCore.Components.Forms.EditContext>","Common.PropertyName":"OnSubmit","Components.EventCallback":"True"}},{"Kind":"Components.Component","Name":"OnValidSubmit","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Forms.EditContext>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<global::Microsoft.AspNetCore.Components.Forms.EditContext>","Common.PropertyName":"OnValidSubmit","Components.EventCallback":"True"}},{"Kind":"Components.Component","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for all child content expressions.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.EditForm","Common.TypeNameIdentifier":"EditForm","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Forms.EditForm.ChildContent","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ChildContent","ParentTag":"EditForm"}],"BoundAttributes":[{"Kind":"Components.ChildContent","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for the 'ChildContent' child content expression.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.EditForm.ChildContent","Common.TypeNameIdentifier":"EditForm","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.IsSpecialKind":"Components.ChildContent","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Forms.EditForm.ChildContent","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ChildContent","ParentTag":"Microsoft.AspNetCore.Components.Forms.EditForm"}],"BoundAttributes":[{"Kind":"Components.ChildContent","Name":"Context","TypeName":"System.String","Documentation":"Specifies the parameter name for the 'ChildContent' child content expression.","Metadata":{"Common.PropertyName":"Context","Components.ChildContentParameterName":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.EditForm.ChildContent","Common.TypeNameIdentifier":"EditForm","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.IsSpecialKind":"Components.ChildContent","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.InputCheckbox","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"InputCheckbox"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"Value","TypeName":"System.Boolean","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Boolean","Common.PropertyName":"Value"}},{"Kind":"Components.Component","Name":"ValueChanged","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.Boolean>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<global::System.Boolean>","Common.PropertyName":"ValueChanged","Components.EventCallback":"True"}},{"Kind":"Components.Component","Name":"ValueExpression","TypeName":"System.Linq.Expressions.Expression<System.Func<System.Boolean>>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Linq.Expressions.Expression<global::System.Func<global::System.Boolean>>","Common.PropertyName":"ValueExpression"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputCheckbox","Common.TypeNameIdentifier":"InputCheckbox","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.InputCheckbox","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.InputCheckbox"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"Value","TypeName":"System.Boolean","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Boolean","Common.PropertyName":"Value"}},{"Kind":"Components.Component","Name":"ValueChanged","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.Boolean>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<global::System.Boolean>","Common.PropertyName":"ValueChanged","Components.EventCallback":"True"}},{"Kind":"Components.Component","Name":"ValueExpression","TypeName":"System.Linq.Expressions.Expression<System.Func<System.Boolean>>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Linq.Expressions.Expression<global::System.Func<global::System.Boolean>>","Common.PropertyName":"ValueExpression"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputCheckbox","Common.TypeNameIdentifier":"InputCheckbox","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.InputDate<TValue>","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"InputDate"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"TValue","TypeName":"System.Type","Documentation":"Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.InputDate<TValue> component.","Metadata":{"Common.PropertyName":"TValue","Components.TypeParameter":"True","Components.TypeParameterIsCascading":"False"}},{"Kind":"Components.Component","Name":"ParsingErrorMessage","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"ParsingErrorMessage"}},{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"Value","TypeName":"TValue","Metadata":{"Common.GloballyQualifiedTypeName":"TValue","Common.PropertyName":"Value","Components.GenericTyped":"True"}},{"Kind":"Components.Component","Name":"ValueChanged","TypeName":"Microsoft.AspNetCore.Components.EventCallback<TValue>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<TValue>","Common.PropertyName":"ValueChanged","Components.EventCallback":"True","Components.GenericTyped":"True"}},{"Kind":"Components.Component","Name":"ValueExpression","TypeName":"System.Linq.Expressions.Expression<System.Func<TValue>>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Linq.Expressions.Expression<global::System.Func<TValue>>","Common.PropertyName":"ValueExpression","Components.GenericTyped":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputDate<TValue>","Common.TypeNameIdentifier":"InputDate","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.GenericTyped":"True","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.InputDate<TValue>","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.InputDate"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"TValue","TypeName":"System.Type","Documentation":"Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.InputDate<TValue> component.","Metadata":{"Common.PropertyName":"TValue","Components.TypeParameter":"True","Components.TypeParameterIsCascading":"False"}},{"Kind":"Components.Component","Name":"ParsingErrorMessage","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"ParsingErrorMessage"}},{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"Value","TypeName":"TValue","Metadata":{"Common.GloballyQualifiedTypeName":"TValue","Common.PropertyName":"Value","Components.GenericTyped":"True"}},{"Kind":"Components.Component","Name":"ValueChanged","TypeName":"Microsoft.AspNetCore.Components.EventCallback<TValue>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<TValue>","Common.PropertyName":"ValueChanged","Components.EventCallback":"True","Components.GenericTyped":"True"}},{"Kind":"Components.Component","Name":"ValueExpression","TypeName":"System.Linq.Expressions.Expression<System.Func<TValue>>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Linq.Expressions.Expression<global::System.Func<TValue>>","Common.PropertyName":"ValueExpression","Components.GenericTyped":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputDate<TValue>","Common.TypeNameIdentifier":"InputDate","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.GenericTyped":"True","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"InputNumber"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"TValue","TypeName":"System.Type","Documentation":"Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.InputNumber<TValue> component.","Metadata":{"Common.PropertyName":"TValue","Components.TypeParameter":"True","Components.TypeParameterIsCascading":"False"}},{"Kind":"Components.Component","Name":"ParsingErrorMessage","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"ParsingErrorMessage"}},{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"Value","TypeName":"TValue","Metadata":{"Common.GloballyQualifiedTypeName":"TValue","Common.PropertyName":"Value","Components.GenericTyped":"True"}},{"Kind":"Components.Component","Name":"ValueChanged","TypeName":"Microsoft.AspNetCore.Components.EventCallback<TValue>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<TValue>","Common.PropertyName":"ValueChanged","Components.EventCallback":"True","Components.GenericTyped":"True"}},{"Kind":"Components.Component","Name":"ValueExpression","TypeName":"System.Linq.Expressions.Expression<System.Func<TValue>>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Linq.Expressions.Expression<global::System.Func<TValue>>","Common.PropertyName":"ValueExpression","Components.GenericTyped":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>","Common.TypeNameIdentifier":"InputNumber","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.GenericTyped":"True","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.InputNumber"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"TValue","TypeName":"System.Type","Documentation":"Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.InputNumber<TValue> component.","Metadata":{"Common.PropertyName":"TValue","Components.TypeParameter":"True","Components.TypeParameterIsCascading":"False"}},{"Kind":"Components.Component","Name":"ParsingErrorMessage","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"ParsingErrorMessage"}},{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"Value","TypeName":"TValue","Metadata":{"Common.GloballyQualifiedTypeName":"TValue","Common.PropertyName":"Value","Components.GenericTyped":"True"}},{"Kind":"Components.Component","Name":"ValueChanged","TypeName":"Microsoft.AspNetCore.Components.EventCallback<TValue>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<TValue>","Common.PropertyName":"ValueChanged","Components.EventCallback":"True","Components.GenericTyped":"True"}},{"Kind":"Components.Component","Name":"ValueExpression","TypeName":"System.Linq.Expressions.Expression<System.Func<TValue>>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Linq.Expressions.Expression<global::System.Func<TValue>>","Common.PropertyName":"ValueExpression","Components.GenericTyped":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>","Common.TypeNameIdentifier":"InputNumber","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.GenericTyped":"True","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"InputSelect"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"TValue","TypeName":"System.Type","Documentation":"Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.InputSelect<TValue> component.","Metadata":{"Common.PropertyName":"TValue","Components.TypeParameter":"True","Components.TypeParameterIsCascading":"False"}},{"Kind":"Components.Component","Name":"ChildContent","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"ChildContent","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"Value","TypeName":"TValue","Metadata":{"Common.GloballyQualifiedTypeName":"TValue","Common.PropertyName":"Value","Components.GenericTyped":"True"}},{"Kind":"Components.Component","Name":"ValueChanged","TypeName":"Microsoft.AspNetCore.Components.EventCallback<TValue>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<TValue>","Common.PropertyName":"ValueChanged","Components.EventCallback":"True","Components.GenericTyped":"True"}},{"Kind":"Components.Component","Name":"ValueExpression","TypeName":"System.Linq.Expressions.Expression<System.Func<TValue>>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Linq.Expressions.Expression<global::System.Func<TValue>>","Common.PropertyName":"ValueExpression","Components.GenericTyped":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>","Common.TypeNameIdentifier":"InputSelect","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.GenericTyped":"True","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.InputSelect"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"TValue","TypeName":"System.Type","Documentation":"Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.InputSelect<TValue> component.","Metadata":{"Common.PropertyName":"TValue","Components.TypeParameter":"True","Components.TypeParameterIsCascading":"False"}},{"Kind":"Components.Component","Name":"ChildContent","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"ChildContent","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"Value","TypeName":"TValue","Metadata":{"Common.GloballyQualifiedTypeName":"TValue","Common.PropertyName":"Value","Components.GenericTyped":"True"}},{"Kind":"Components.Component","Name":"ValueChanged","TypeName":"Microsoft.AspNetCore.Components.EventCallback<TValue>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<TValue>","Common.PropertyName":"ValueChanged","Components.EventCallback":"True","Components.GenericTyped":"True"}},{"Kind":"Components.Component","Name":"ValueExpression","TypeName":"System.Linq.Expressions.Expression<System.Func<TValue>>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Linq.Expressions.Expression<global::System.Func<TValue>>","Common.PropertyName":"ValueExpression","Components.GenericTyped":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>","Common.TypeNameIdentifier":"InputSelect","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.GenericTyped":"True","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>.ChildContent","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ChildContent","ParentTag":"InputSelect"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>.ChildContent","Common.TypeNameIdentifier":"InputSelect","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.IsSpecialKind":"Components.ChildContent","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>.ChildContent","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ChildContent","ParentTag":"Microsoft.AspNetCore.Components.Forms.InputSelect"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>.ChildContent","Common.TypeNameIdentifier":"InputSelect","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.IsSpecialKind":"Components.ChildContent","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.InputText","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"InputText"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"Value","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"Value"}},{"Kind":"Components.Component","Name":"ValueChanged","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.String>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<global::System.String>","Common.PropertyName":"ValueChanged","Components.EventCallback":"True"}},{"Kind":"Components.Component","Name":"ValueExpression","TypeName":"System.Linq.Expressions.Expression<System.Func<System.String>>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Linq.Expressions.Expression<global::System.Func<global::System.String>>","Common.PropertyName":"ValueExpression"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputText","Common.TypeNameIdentifier":"InputText","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.InputText","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.InputText"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"Value","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"Value"}},{"Kind":"Components.Component","Name":"ValueChanged","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.String>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<global::System.String>","Common.PropertyName":"ValueChanged","Components.EventCallback":"True"}},{"Kind":"Components.Component","Name":"ValueExpression","TypeName":"System.Linq.Expressions.Expression<System.Func<System.String>>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Linq.Expressions.Expression<global::System.Func<global::System.String>>","Common.PropertyName":"ValueExpression"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputText","Common.TypeNameIdentifier":"InputText","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.InputTextArea","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"InputTextArea"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"Value","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"Value"}},{"Kind":"Components.Component","Name":"ValueChanged","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.String>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<global::System.String>","Common.PropertyName":"ValueChanged","Components.EventCallback":"True"}},{"Kind":"Components.Component","Name":"ValueExpression","TypeName":"System.Linq.Expressions.Expression<System.Func<System.String>>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Linq.Expressions.Expression<global::System.Func<global::System.String>>","Common.PropertyName":"ValueExpression"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputTextArea","Common.TypeNameIdentifier":"InputTextArea","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.InputTextArea","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.InputTextArea"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"Value","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"Value"}},{"Kind":"Components.Component","Name":"ValueChanged","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.String>","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.EventCallback<global::System.String>","Common.PropertyName":"ValueChanged","Components.EventCallback":"True"}},{"Kind":"Components.Component","Name":"ValueExpression","TypeName":"System.Linq.Expressions.Expression<System.Func<System.String>>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Linq.Expressions.Expression<global::System.Func<global::System.String>>","Common.PropertyName":"ValueExpression"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputTextArea","Common.TypeNameIdentifier":"InputTextArea","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.ValidationMessage<TValue>","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ValidationMessage"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"TValue","TypeName":"System.Type","Documentation":"Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.ValidationMessage<TValue> component.","Metadata":{"Common.PropertyName":"TValue","Components.TypeParameter":"True","Components.TypeParameterIsCascading":"False"}},{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"For","TypeName":"System.Linq.Expressions.Expression<System.Func<TValue>>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Linq.Expressions.Expression<global::System.Func<TValue>>","Common.PropertyName":"For","Components.GenericTyped":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.ValidationMessage<TValue>","Common.TypeNameIdentifier":"ValidationMessage","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.GenericTyped":"True","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.ValidationMessage<TValue>","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.ValidationMessage"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"TValue","TypeName":"System.Type","Documentation":"Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.ValidationMessage<TValue> component.","Metadata":{"Common.PropertyName":"TValue","Components.TypeParameter":"True","Components.TypeParameterIsCascading":"False"}},{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"For","TypeName":"System.Linq.Expressions.Expression<System.Func<TValue>>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Linq.Expressions.Expression<global::System.Func<TValue>>","Common.PropertyName":"For","Components.GenericTyped":"True"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.ValidationMessage<TValue>","Common.TypeNameIdentifier":"ValidationMessage","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.GenericTyped":"True","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.ValidationSummary","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ValidationSummary"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"Model","TypeName":"System.Object","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Object","Common.PropertyName":"Model"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.ValidationSummary","Common.TypeNameIdentifier":"ValidationSummary","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Forms.ValidationSummary","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.ValidationSummary"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"Model","TypeName":"System.Object","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Object","Common.PropertyName":"Model"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.ValidationSummary","Common.TypeNameIdentifier":"ValidationSummary","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Routing.NavLink","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"NavLink"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"ActiveClass","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"ActiveClass"}},{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"ChildContent","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"ChildContent","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"Match","TypeName":"Microsoft.AspNetCore.Components.Routing.NavLinkMatch","IsEnum":true,"Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.Routing.NavLinkMatch","Common.PropertyName":"Match"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Routing.NavLink","Common.TypeNameIdentifier":"NavLink","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Routing","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.Component","Name":"Microsoft.AspNetCore.Components.Routing.NavLink","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Routing.NavLink"}],"BoundAttributes":[{"Kind":"Components.Component","Name":"ActiveClass","TypeName":"System.String","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.String","Common.PropertyName":"ActiveClass"}},{"Kind":"Components.Component","Name":"AdditionalAttributes","TypeName":"System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>","Metadata":{"Common.GloballyQualifiedTypeName":"global::System.Collections.Generic.IReadOnlyDictionary<global::System.String, global::System.Object>","Common.PropertyName":"AdditionalAttributes"}},{"Kind":"Components.Component","Name":"ChildContent","TypeName":"Microsoft.AspNetCore.Components.RenderFragment","Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.RenderFragment","Common.PropertyName":"ChildContent","Components.ChildContent":"True"}},{"Kind":"Components.Component","Name":"Match","TypeName":"Microsoft.AspNetCore.Components.Routing.NavLinkMatch","IsEnum":true,"Metadata":{"Common.GloballyQualifiedTypeName":"global::Microsoft.AspNetCore.Components.Routing.NavLinkMatch","Common.PropertyName":"Match"}}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Routing.NavLink","Common.TypeNameIdentifier":"NavLink","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Routing","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.IComponent"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Routing.NavLink.ChildContent","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ChildContent","ParentTag":"NavLink"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Routing.NavLink.ChildContent","Common.TypeNameIdentifier":"NavLink","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Routing","Components.IsSpecialKind":"Components.ChildContent","Runtime.Name":"Components.None"}},{"Kind":"Components.ChildContent","Name":"Microsoft.AspNetCore.Components.Routing.NavLink.ChildContent","AssemblyName":"Microsoft.AspNetCore.Components.Web","CaseSensitive":true,"TagMatchingRules":[{"TagName":"ChildContent","ParentTag":"Microsoft.AspNetCore.Components.Routing.NavLink"}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Routing.NavLink.ChildContent","Common.TypeNameIdentifier":"NavLink","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Routing","Components.IsSpecialKind":"Components.ChildContent","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onabort","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onabort' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onabort","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onabort:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onabort:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onabort","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ProgressEventArgs>","Documentation":"Sets the '@onabort' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onabort","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onabort' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onabort' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.ProgressEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onactivate","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onactivate","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onactivate:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onactivate:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onactivate","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onactivate","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onactivate' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onactivate' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onbeforeactivate","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onbeforeactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onbeforeactivate","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onbeforeactivate:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onbeforeactivate:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onbeforeactivate","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onbeforeactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onbeforeactivate","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onbeforeactivate' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onbeforeactivate' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onbeforecopy","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onbeforecopy' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onbeforecopy","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onbeforecopy:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onbeforecopy:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onbeforecopy","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onbeforecopy' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onbeforecopy","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onbeforecopy' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onbeforecopy' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onbeforecut","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onbeforecut' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onbeforecut","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onbeforecut:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onbeforecut:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onbeforecut","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onbeforecut' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onbeforecut","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onbeforecut' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onbeforecut' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onbeforedeactivate","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onbeforedeactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onbeforedeactivate","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onbeforedeactivate:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onbeforedeactivate:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onbeforedeactivate","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onbeforedeactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onbeforedeactivate","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onbeforedeactivate' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onbeforedeactivate' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onbeforepaste","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onbeforepaste' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onbeforepaste","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onbeforepaste:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onbeforepaste:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onbeforepaste","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onbeforepaste' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onbeforepaste","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onbeforepaste' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onbeforepaste' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onblur","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onblur' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onblur","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onblur:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onblur:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onblur","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.FocusEventArgs>","Documentation":"Sets the '@onblur' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onblur","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onblur' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onblur' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.FocusEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"oncanplay","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@oncanplay' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@oncanplay","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oncanplay:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oncanplay:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@oncanplay","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@oncanplay' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"oncanplay","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@oncanplay' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@oncanplay' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"oncanplaythrough","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@oncanplaythrough' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@oncanplaythrough","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oncanplaythrough:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oncanplaythrough:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@oncanplaythrough","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@oncanplaythrough' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"oncanplaythrough","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@oncanplaythrough' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@oncanplaythrough' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onchange","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onchange' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.ChangeEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onchange","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onchange:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onchange:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onchange","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.ChangeEventArgs>","Documentation":"Sets the '@onchange' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.ChangeEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onchange","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onchange' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onchange' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.ChangeEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onclick","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onclick' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onclick","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onclick:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onclick:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onclick","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>","Documentation":"Sets the '@onclick' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onclick","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onclick' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onclick' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.MouseEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"oncontextmenu","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@oncontextmenu' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@oncontextmenu","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oncontextmenu:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oncontextmenu:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@oncontextmenu","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>","Documentation":"Sets the '@oncontextmenu' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"oncontextmenu","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@oncontextmenu' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@oncontextmenu' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.MouseEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"oncopy","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@oncopy' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ClipboardEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@oncopy","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oncopy:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oncopy:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@oncopy","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ClipboardEventArgs>","Documentation":"Sets the '@oncopy' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ClipboardEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"oncopy","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@oncopy' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@oncopy' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.ClipboardEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"oncuechange","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@oncuechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@oncuechange","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oncuechange:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oncuechange:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@oncuechange","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@oncuechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"oncuechange","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@oncuechange' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@oncuechange' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"oncut","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@oncut' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ClipboardEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@oncut","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oncut:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oncut:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@oncut","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ClipboardEventArgs>","Documentation":"Sets the '@oncut' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ClipboardEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"oncut","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@oncut' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@oncut' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.ClipboardEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ondblclick","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ondblclick' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ondblclick","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondblclick:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondblclick:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ondblclick","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>","Documentation":"Sets the '@ondblclick' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ondblclick","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondblclick' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ondblclick' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.MouseEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ondeactivate","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ondeactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ondeactivate","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondeactivate:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondeactivate:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ondeactivate","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@ondeactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ondeactivate","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondeactivate' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ondeactivate' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ondrag","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ondrag' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ondrag","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondrag:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondrag:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ondrag","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.DragEventArgs>","Documentation":"Sets the '@ondrag' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ondrag","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondrag' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ondrag' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.DragEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ondragend","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ondragend' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ondragend","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondragend:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondragend:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ondragend","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.DragEventArgs>","Documentation":"Sets the '@ondragend' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ondragend","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondragend' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ondragend' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.DragEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ondragenter","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ondragenter' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ondragenter","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondragenter:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondragenter:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ondragenter","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.DragEventArgs>","Documentation":"Sets the '@ondragenter' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ondragenter","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondragenter' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ondragenter' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.DragEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ondragleave","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ondragleave' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ondragleave","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondragleave:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondragleave:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ondragleave","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.DragEventArgs>","Documentation":"Sets the '@ondragleave' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ondragleave","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondragleave' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ondragleave' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.DragEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ondragover","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ondragover' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ondragover","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondragover:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondragover:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ondragover","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.DragEventArgs>","Documentation":"Sets the '@ondragover' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ondragover","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondragover' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ondragover' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.DragEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ondragstart","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ondragstart' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ondragstart","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondragstart:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondragstart:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ondragstart","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.DragEventArgs>","Documentation":"Sets the '@ondragstart' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ondragstart","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondragstart' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ondragstart' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.DragEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ondrop","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ondrop' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ondrop","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondrop:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondrop:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ondrop","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.DragEventArgs>","Documentation":"Sets the '@ondrop' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ondrop","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondrop' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ondrop' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.DragEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ondurationchange","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ondurationchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ondurationchange","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondurationchange:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ondurationchange:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ondurationchange","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@ondurationchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ondurationchange","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondurationchange' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ondurationchange' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onemptied","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onemptied' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onemptied","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onemptied:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onemptied:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onemptied","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onemptied' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onemptied","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onemptied' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onemptied' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onended","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onended' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onended","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onended:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onended:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onended","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onended' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onended","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onended' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onended' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onerror","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onerror' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ErrorEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onerror","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onerror:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onerror:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onerror","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ErrorEventArgs>","Documentation":"Sets the '@onerror' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ErrorEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onerror","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onerror' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onerror' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.ErrorEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onfocus","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onfocus' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onfocus","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onfocus:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onfocus:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onfocus","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.FocusEventArgs>","Documentation":"Sets the '@onfocus' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onfocus","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onfocus' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onfocus' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.FocusEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onfocusin","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onfocusin' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onfocusin","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onfocusin:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onfocusin:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onfocusin","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.FocusEventArgs>","Documentation":"Sets the '@onfocusin' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onfocusin","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onfocusin' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onfocusin' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.FocusEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onfocusout","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onfocusout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onfocusout","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onfocusout:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onfocusout:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onfocusout","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.FocusEventArgs>","Documentation":"Sets the '@onfocusout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onfocusout","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onfocusout' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onfocusout' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.FocusEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onfullscreenchange","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onfullscreenchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onfullscreenchange","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onfullscreenchange:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onfullscreenchange:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onfullscreenchange","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onfullscreenchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onfullscreenchange","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onfullscreenchange' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onfullscreenchange' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onfullscreenerror","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onfullscreenerror' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onfullscreenerror","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onfullscreenerror:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onfullscreenerror:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onfullscreenerror","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onfullscreenerror' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onfullscreenerror","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onfullscreenerror' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onfullscreenerror' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ongotpointercapture","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ongotpointercapture' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ongotpointercapture","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ongotpointercapture:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ongotpointercapture:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ongotpointercapture","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>","Documentation":"Sets the '@ongotpointercapture' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ongotpointercapture","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ongotpointercapture' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ongotpointercapture' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.PointerEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"oninput","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@oninput' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.ChangeEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@oninput","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oninput:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oninput:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@oninput","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.ChangeEventArgs>","Documentation":"Sets the '@oninput' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.ChangeEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"oninput","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@oninput' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@oninput' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.ChangeEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"oninvalid","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@oninvalid' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@oninvalid","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oninvalid:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@oninvalid:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@oninvalid","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@oninvalid' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"oninvalid","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@oninvalid' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@oninvalid' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onkeydown","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onkeydown' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.KeyboardEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onkeydown","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onkeydown:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onkeydown:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onkeydown","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.KeyboardEventArgs>","Documentation":"Sets the '@onkeydown' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.KeyboardEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onkeydown","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onkeydown' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onkeydown' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.KeyboardEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onkeypress","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onkeypress' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.KeyboardEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onkeypress","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onkeypress:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onkeypress:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onkeypress","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.KeyboardEventArgs>","Documentation":"Sets the '@onkeypress' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.KeyboardEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onkeypress","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onkeypress' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onkeypress' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.KeyboardEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onkeyup","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onkeyup' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.KeyboardEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onkeyup","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onkeyup:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onkeyup:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onkeyup","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.KeyboardEventArgs>","Documentation":"Sets the '@onkeyup' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.KeyboardEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onkeyup","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onkeyup' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onkeyup' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.KeyboardEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onload","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onload' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onload","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onload:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onload:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onload","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ProgressEventArgs>","Documentation":"Sets the '@onload' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onload","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onload' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onload' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.ProgressEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onloadeddata","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onloadeddata' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onloadeddata","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onloadeddata:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onloadeddata:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onloadeddata","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onloadeddata' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onloadeddata","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onloadeddata' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onloadeddata' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onloadedmetadata","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onloadedmetadata' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onloadedmetadata","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onloadedmetadata:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onloadedmetadata:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onloadedmetadata","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onloadedmetadata' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onloadedmetadata","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onloadedmetadata' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onloadedmetadata' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onloadend","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onloadend' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onloadend","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onloadend:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onloadend:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onloadend","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ProgressEventArgs>","Documentation":"Sets the '@onloadend' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onloadend","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onloadend' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onloadend' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.ProgressEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onloadstart","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onloadstart' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onloadstart","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onloadstart:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onloadstart:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onloadstart","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ProgressEventArgs>","Documentation":"Sets the '@onloadstart' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onloadstart","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onloadstart' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onloadstart' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.ProgressEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onlostpointercapture","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onlostpointercapture' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onlostpointercapture","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onlostpointercapture:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onlostpointercapture:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onlostpointercapture","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>","Documentation":"Sets the '@onlostpointercapture' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onlostpointercapture","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onlostpointercapture' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onlostpointercapture' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.PointerEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onmousedown","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onmousedown' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onmousedown","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onmousedown:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onmousedown:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onmousedown","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>","Documentation":"Sets the '@onmousedown' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onmousedown","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onmousedown' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onmousedown' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.MouseEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onmousemove","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onmousemove' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onmousemove","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onmousemove:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onmousemove:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onmousemove","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>","Documentation":"Sets the '@onmousemove' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onmousemove","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onmousemove' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onmousemove' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.MouseEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onmouseout","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onmouseout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onmouseout","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onmouseout:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onmouseout:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onmouseout","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>","Documentation":"Sets the '@onmouseout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onmouseout","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onmouseout' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onmouseout' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.MouseEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onmouseover","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onmouseover' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onmouseover","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onmouseover:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onmouseover:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onmouseover","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>","Documentation":"Sets the '@onmouseover' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onmouseover","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onmouseover' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onmouseover' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.MouseEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onmouseup","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onmouseup' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onmouseup","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onmouseup:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onmouseup:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onmouseup","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>","Documentation":"Sets the '@onmouseup' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onmouseup","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onmouseup' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onmouseup' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.MouseEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onmousewheel","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onmousewheel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.WheelEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onmousewheel","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onmousewheel:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onmousewheel:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onmousewheel","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.WheelEventArgs>","Documentation":"Sets the '@onmousewheel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.WheelEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onmousewheel","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onmousewheel' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onmousewheel' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.WheelEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onpaste","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onpaste' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ClipboardEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onpaste","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpaste:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpaste:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onpaste","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ClipboardEventArgs>","Documentation":"Sets the '@onpaste' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ClipboardEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onpaste","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpaste' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onpaste' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.ClipboardEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onpause","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onpause' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onpause","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpause:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpause:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onpause","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onpause' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onpause","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpause' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onpause' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onplay","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onplay' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onplay","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onplay:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onplay:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onplay","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onplay' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onplay","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onplay' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onplay' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onplaying","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onplaying' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onplaying","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onplaying:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onplaying:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onplaying","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onplaying' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onplaying","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onplaying' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onplaying' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onpointercancel","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onpointercancel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onpointercancel","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointercancel:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointercancel:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onpointercancel","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>","Documentation":"Sets the '@onpointercancel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onpointercancel","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointercancel' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onpointercancel' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.PointerEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onpointerdown","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onpointerdown' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onpointerdown","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerdown:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerdown:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onpointerdown","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>","Documentation":"Sets the '@onpointerdown' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onpointerdown","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerdown' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onpointerdown' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.PointerEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onpointerenter","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onpointerenter' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onpointerenter","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerenter:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerenter:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onpointerenter","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>","Documentation":"Sets the '@onpointerenter' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onpointerenter","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerenter' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onpointerenter' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.PointerEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onpointerleave","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onpointerleave' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onpointerleave","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerleave:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerleave:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onpointerleave","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>","Documentation":"Sets the '@onpointerleave' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onpointerleave","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerleave' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onpointerleave' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.PointerEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onpointerlockchange","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onpointerlockchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onpointerlockchange","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerlockchange:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerlockchange:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onpointerlockchange","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onpointerlockchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onpointerlockchange","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerlockchange' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onpointerlockchange' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onpointerlockerror","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onpointerlockerror' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onpointerlockerror","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerlockerror:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerlockerror:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onpointerlockerror","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onpointerlockerror' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onpointerlockerror","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerlockerror' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onpointerlockerror' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onpointermove","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onpointermove' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onpointermove","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointermove:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointermove:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onpointermove","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>","Documentation":"Sets the '@onpointermove' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onpointermove","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointermove' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onpointermove' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.PointerEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onpointerout","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onpointerout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onpointerout","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerout:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerout:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onpointerout","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>","Documentation":"Sets the '@onpointerout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onpointerout","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerout' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onpointerout' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.PointerEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onpointerover","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onpointerover' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onpointerover","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerover:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerover:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onpointerover","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>","Documentation":"Sets the '@onpointerover' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onpointerover","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerover' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onpointerover' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.PointerEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onpointerup","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onpointerup' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onpointerup","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerup:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onpointerup:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onpointerup","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>","Documentation":"Sets the '@onpointerup' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onpointerup","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerup' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onpointerup' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.PointerEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onprogress","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onprogress' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onprogress","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onprogress:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onprogress:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onprogress","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ProgressEventArgs>","Documentation":"Sets the '@onprogress' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onprogress","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onprogress' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onprogress' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.ProgressEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onratechange","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onratechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onratechange","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onratechange:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onratechange:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onratechange","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onratechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onratechange","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onratechange' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onratechange' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onreadystatechange","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onreadystatechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onreadystatechange","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onreadystatechange:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onreadystatechange:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onreadystatechange","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onreadystatechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onreadystatechange","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onreadystatechange' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onreadystatechange' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onreset","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onreset' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onreset","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onreset:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onreset:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onreset","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onreset' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onreset","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onreset' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onreset' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onscroll","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onscroll' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onscroll","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onscroll:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onscroll:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onscroll","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onscroll' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onscroll","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onscroll' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onscroll' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onseeked","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onseeked' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onseeked","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onseeked:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onseeked:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onseeked","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onseeked' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onseeked","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onseeked' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onseeked' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onseeking","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onseeking' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onseeking","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onseeking:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onseeking:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onseeking","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onseeking' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onseeking","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onseeking' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onseeking' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onselect","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onselect' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onselect","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onselect:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onselect:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onselect","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onselect' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onselect","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onselect' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onselect' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onselectionchange","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onselectionchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onselectionchange","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onselectionchange:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onselectionchange:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onselectionchange","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onselectionchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onselectionchange","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onselectionchange' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onselectionchange' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onselectstart","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onselectstart' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onselectstart","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onselectstart:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onselectstart:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onselectstart","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onselectstart' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onselectstart","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onselectstart' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onselectstart' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onstalled","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onstalled' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onstalled","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onstalled:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onstalled:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onstalled","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onstalled' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onstalled","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onstalled' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onstalled' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onstop","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onstop' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onstop","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onstop:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onstop:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onstop","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onstop' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onstop","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onstop' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onstop' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onsubmit","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onsubmit' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onsubmit","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onsubmit:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onsubmit:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onsubmit","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onsubmit' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onsubmit","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onsubmit' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onsubmit' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onsuspend","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onsuspend' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onsuspend","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onsuspend:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onsuspend:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onsuspend","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onsuspend' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onsuspend","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onsuspend' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onsuspend' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ontimeout","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ontimeout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ontimeout","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontimeout:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontimeout:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ontimeout","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ProgressEventArgs>","Documentation":"Sets the '@ontimeout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ontimeout","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontimeout' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ontimeout' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.ProgressEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ontimeupdate","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ontimeupdate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ontimeupdate","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontimeupdate:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontimeupdate:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ontimeupdate","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@ontimeupdate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ontimeupdate","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontimeupdate' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ontimeupdate' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ontouchcancel","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ontouchcancel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ontouchcancel","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontouchcancel:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontouchcancel:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ontouchcancel","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.TouchEventArgs>","Documentation":"Sets the '@ontouchcancel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ontouchcancel","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontouchcancel' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ontouchcancel' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.TouchEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ontouchend","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ontouchend' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ontouchend","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontouchend:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontouchend:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ontouchend","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.TouchEventArgs>","Documentation":"Sets the '@ontouchend' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ontouchend","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontouchend' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ontouchend' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.TouchEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ontouchenter","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ontouchenter' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ontouchenter","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontouchenter:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontouchenter:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ontouchenter","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.TouchEventArgs>","Documentation":"Sets the '@ontouchenter' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ontouchenter","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontouchenter' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ontouchenter' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.TouchEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ontouchleave","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ontouchleave' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ontouchleave","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontouchleave:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontouchleave:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ontouchleave","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.TouchEventArgs>","Documentation":"Sets the '@ontouchleave' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ontouchleave","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontouchleave' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ontouchleave' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.TouchEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ontouchmove","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ontouchmove' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ontouchmove","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontouchmove:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontouchmove:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ontouchmove","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.TouchEventArgs>","Documentation":"Sets the '@ontouchmove' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ontouchmove","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontouchmove' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ontouchmove' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.TouchEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"ontouchstart","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@ontouchstart' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ontouchstart","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontouchstart:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@ontouchstart:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@ontouchstart","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.TouchEventArgs>","Documentation":"Sets the '@ontouchstart' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"ontouchstart","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontouchstart' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@ontouchstart' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.TouchEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onvolumechange","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onvolumechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onvolumechange","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onvolumechange:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onvolumechange:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onvolumechange","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onvolumechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onvolumechange","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onvolumechange' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onvolumechange' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onwaiting","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onwaiting' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onwaiting","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onwaiting:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onwaiting:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onwaiting","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>","Documentation":"Sets the '@onwaiting' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onwaiting","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onwaiting' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onwaiting' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"System.EventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.EventHandler","Name":"onwheel","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Sets the '@onwheel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.WheelEventArgs'.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@onwheel","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onwheel:preventDefault","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"*","Attributes":[{"Name":"@onwheel:stopPropagation","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.EventHandler","Name":"@onwheel","TypeName":"Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.WheelEventArgs>","Documentation":"Sets the '@onwheel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.WheelEventArgs'.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"onwheel","Components.IsWeaklyTyped":"True"},"Parameters":[{"Name":"preventDefault","TypeName":"System.Boolean","Documentation":"Specifies whether to cancel (if cancelable) the default action that belongs to the '@onwheel' event.","Metadata":{"Common.PropertyName":"PreventDefault"}},{"Name":"stopPropagation","TypeName":"System.Boolean","Documentation":"Specifies whether to prevent further propagation of the '@onwheel' event in the capturing and bubbling phases.","Metadata":{"Common.PropertyName":"StopPropagation"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.EventHandlers","Common.TypeNameIdentifier":"EventHandlers","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.EventHandler.EventArgs":"Microsoft.AspNetCore.Components.Web.WheelEventArgs","Components.IsSpecialKind":"Components.EventHandler","Runtime.Name":"Components.None"}},{"Kind":"Components.Splat","Name":"Attributes","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Merges a collection of attributes into the current element or component.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@attributes","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Splat","Name":"@attributes","TypeName":"System.Object","Documentation":"Merges a collection of attributes into the current element or component.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Attributes"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Attributes","Components.IsSpecialKind":"Components.Splat","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to an attribute and a change event, based on the naming of the bind attribute. For example: <code>@bind-value=\"...\"</code> and <code>@bind-value:event=\"onchange\"</code> will assign the current value of the expression to the 'value' attribute, and assign a delegate that attempts to set the value to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@bind-","NameComparison":1,"Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-...","TypeName":"System.Collections.Generic.Dictionary<string, object>","IndexerNamePrefix":"@bind-","IndexerTypeName":"System.Object","Documentation":"Binds the provided expression to an attribute and a change event, based on the naming of the bind attribute. For example: <code>@bind-value=\"...\"</code> and <code>@bind-value:event=\"onchange\"</code> will assign the current value of the expression to the 'value' attribute, and assign a delegate that attempts to set the value to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the corresponding bind attribute. For example: <code>@bind-value:format=\"...\"</code> will apply a format string to the value specified in <code>@bind-value=\"...\"</code>. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind-...' attribute.","Metadata":{"Common.PropertyName":"Event"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Bind","Common.TypeNameIdentifier":"Bind","Common.TypeNamespace":"Microsoft.AspNetCore.Components","Components.Bind.Fallback":"True","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Microsoft.AspNetCore.Components.Forms.InputCheckbox","AssemblyName":"Microsoft.AspNetCore.Components.Web","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"InputCheckbox","Attributes":[{"Name":"@bind-Value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"InputCheckbox","Attributes":[{"Name":"@bind-Value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-Value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-Value","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.Boolean>","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Value"},"Parameters":[{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputCheckbox","Common.TypeNameIdentifier":"InputCheckbox","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.Bind.ChangeAttribute":"ValueChanged","Components.Bind.ExpressionAttribute":"ValueExpression","Components.Bind.ValueAttribute":"Value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Microsoft.AspNetCore.Components.Forms.InputCheckbox","AssemblyName":"Microsoft.AspNetCore.Components.Web","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.InputCheckbox","Attributes":[{"Name":"@bind-Value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"Microsoft.AspNetCore.Components.Forms.InputCheckbox","Attributes":[{"Name":"@bind-Value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-Value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-Value","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.Boolean>","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Value"},"Parameters":[{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputCheckbox","Common.TypeNameIdentifier":"InputCheckbox","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.Bind.ChangeAttribute":"ValueChanged","Components.Bind.ExpressionAttribute":"ValueExpression","Components.Bind.ValueAttribute":"Value","Components.IsSpecialKind":"Components.Bind","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Microsoft.AspNetCore.Components.Forms.InputDate<TValue>","AssemblyName":"Microsoft.AspNetCore.Components.Web","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"InputDate","Attributes":[{"Name":"@bind-Value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"InputDate","Attributes":[{"Name":"@bind-Value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-Value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-Value","TypeName":"Microsoft.AspNetCore.Components.EventCallback<TValue>","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Value"},"Parameters":[{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputDate<TValue>","Common.TypeNameIdentifier":"InputDate","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.Bind.ChangeAttribute":"ValueChanged","Components.Bind.ExpressionAttribute":"ValueExpression","Components.Bind.ValueAttribute":"Value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Microsoft.AspNetCore.Components.Forms.InputDate<TValue>","AssemblyName":"Microsoft.AspNetCore.Components.Web","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.InputDate","Attributes":[{"Name":"@bind-Value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"Microsoft.AspNetCore.Components.Forms.InputDate","Attributes":[{"Name":"@bind-Value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-Value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-Value","TypeName":"Microsoft.AspNetCore.Components.EventCallback<TValue>","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Value"},"Parameters":[{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputDate<TValue>","Common.TypeNameIdentifier":"InputDate","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.Bind.ChangeAttribute":"ValueChanged","Components.Bind.ExpressionAttribute":"ValueExpression","Components.Bind.ValueAttribute":"Value","Components.IsSpecialKind":"Components.Bind","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>","AssemblyName":"Microsoft.AspNetCore.Components.Web","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"InputNumber","Attributes":[{"Name":"@bind-Value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"InputNumber","Attributes":[{"Name":"@bind-Value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-Value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-Value","TypeName":"Microsoft.AspNetCore.Components.EventCallback<TValue>","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Value"},"Parameters":[{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>","Common.TypeNameIdentifier":"InputNumber","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.Bind.ChangeAttribute":"ValueChanged","Components.Bind.ExpressionAttribute":"ValueExpression","Components.Bind.ValueAttribute":"Value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>","AssemblyName":"Microsoft.AspNetCore.Components.Web","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.InputNumber","Attributes":[{"Name":"@bind-Value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"Microsoft.AspNetCore.Components.Forms.InputNumber","Attributes":[{"Name":"@bind-Value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-Value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-Value","TypeName":"Microsoft.AspNetCore.Components.EventCallback<TValue>","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Value"},"Parameters":[{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>","Common.TypeNameIdentifier":"InputNumber","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.Bind.ChangeAttribute":"ValueChanged","Components.Bind.ExpressionAttribute":"ValueExpression","Components.Bind.ValueAttribute":"Value","Components.IsSpecialKind":"Components.Bind","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>","AssemblyName":"Microsoft.AspNetCore.Components.Web","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"InputSelect","Attributes":[{"Name":"@bind-Value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"InputSelect","Attributes":[{"Name":"@bind-Value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-Value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-Value","TypeName":"Microsoft.AspNetCore.Components.EventCallback<TValue>","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Value"},"Parameters":[{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>","Common.TypeNameIdentifier":"InputSelect","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.Bind.ChangeAttribute":"ValueChanged","Components.Bind.ExpressionAttribute":"ValueExpression","Components.Bind.ValueAttribute":"Value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>","AssemblyName":"Microsoft.AspNetCore.Components.Web","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.InputSelect","Attributes":[{"Name":"@bind-Value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"Microsoft.AspNetCore.Components.Forms.InputSelect","Attributes":[{"Name":"@bind-Value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-Value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-Value","TypeName":"Microsoft.AspNetCore.Components.EventCallback<TValue>","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Value"},"Parameters":[{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>","Common.TypeNameIdentifier":"InputSelect","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.Bind.ChangeAttribute":"ValueChanged","Components.Bind.ExpressionAttribute":"ValueExpression","Components.Bind.ValueAttribute":"Value","Components.IsSpecialKind":"Components.Bind","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Microsoft.AspNetCore.Components.Forms.InputText","AssemblyName":"Microsoft.AspNetCore.Components.Web","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"InputText","Attributes":[{"Name":"@bind-Value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"InputText","Attributes":[{"Name":"@bind-Value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-Value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-Value","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.String>","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Value"},"Parameters":[{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputText","Common.TypeNameIdentifier":"InputText","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.Bind.ChangeAttribute":"ValueChanged","Components.Bind.ExpressionAttribute":"ValueExpression","Components.Bind.ValueAttribute":"Value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Microsoft.AspNetCore.Components.Forms.InputText","AssemblyName":"Microsoft.AspNetCore.Components.Web","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.InputText","Attributes":[{"Name":"@bind-Value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"Microsoft.AspNetCore.Components.Forms.InputText","Attributes":[{"Name":"@bind-Value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-Value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-Value","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.String>","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Value"},"Parameters":[{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputText","Common.TypeNameIdentifier":"InputText","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.Bind.ChangeAttribute":"ValueChanged","Components.Bind.ExpressionAttribute":"ValueExpression","Components.Bind.ValueAttribute":"Value","Components.IsSpecialKind":"Components.Bind","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Microsoft.AspNetCore.Components.Forms.InputTextArea","AssemblyName":"Microsoft.AspNetCore.Components.Web","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"InputTextArea","Attributes":[{"Name":"@bind-Value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"InputTextArea","Attributes":[{"Name":"@bind-Value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-Value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-Value","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.String>","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Value"},"Parameters":[{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputTextArea","Common.TypeNameIdentifier":"InputTextArea","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.Bind.ChangeAttribute":"ValueChanged","Components.Bind.ExpressionAttribute":"ValueExpression","Components.Bind.ValueAttribute":"Value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Microsoft.AspNetCore.Components.Forms.InputTextArea","AssemblyName":"Microsoft.AspNetCore.Components.Web","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"Microsoft.AspNetCore.Components.Forms.InputTextArea","Attributes":[{"Name":"@bind-Value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"Microsoft.AspNetCore.Components.Forms.InputTextArea","Attributes":[{"Name":"@bind-Value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-Value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-Value","TypeName":"Microsoft.AspNetCore.Components.EventCallback<System.String>","Documentation":"Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Value"},"Parameters":[{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]}],"Metadata":{"Common.TypeName":"Microsoft.AspNetCore.Components.Forms.InputTextArea","Common.TypeNameIdentifier":"InputTextArea","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Forms","Components.Bind.ChangeAttribute":"ValueChanged","Components.Bind.ExpressionAttribute":"ValueExpression","Components.Bind.ValueAttribute":"Value","Components.IsSpecialKind":"Components.Bind","Components.NameMatch":"Components.FullyQualifiedNameMatch","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"select","Attributes":[{"Name":"@bind","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"select","Attributes":[{"Name":"@bind:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":null,"Components.Bind.IsInvariantCulture":"False","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"textarea","Attributes":[{"Name":"@bind","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"textarea","Attributes":[{"Name":"@bind:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":null,"Components.Bind.IsInvariantCulture":"False","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'checked' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"input","Attributes":[{"Name":"type","Value":"checkbox","ValueComparison":1},{"Name":"@bind","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"input","Attributes":[{"Name":"type","Value":"checkbox","ValueComparison":1},{"Name":"@bind:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'checked' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_checked"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.","Metadata":{"Common.PropertyName":"Event_checked"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-checked","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_checked"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":null,"Components.Bind.IsInvariantCulture":"False","Components.Bind.TypeAttribute":"checkbox","Components.Bind.ValueAttribute":"checked","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind_value","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"input","Attributes":[{"Name":"type","Value":"date","ValueComparison":1},{"Name":"@bind-value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"input","Attributes":[{"Name":"type","Value":"date","ValueComparison":1},{"Name":"@bind-value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-value","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind_value"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind-value' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":"yyyy-MM-dd","Components.Bind.IsInvariantCulture":"True","Components.Bind.TypeAttribute":"date","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"input","Attributes":[{"Name":"type","Value":"date","ValueComparison":1},{"Name":"@bind","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"input","Attributes":[{"Name":"type","Value":"date","ValueComparison":1},{"Name":"@bind:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":"yyyy-MM-dd","Components.Bind.IsInvariantCulture":"True","Components.Bind.TypeAttribute":"date","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind_value","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"input","Attributes":[{"Name":"type","Value":"datetime-local","ValueComparison":1},{"Name":"@bind-value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"input","Attributes":[{"Name":"type","Value":"datetime-local","ValueComparison":1},{"Name":"@bind-value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-value","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind_value"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind-value' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":"yyyy-MM-ddTHH:mm:ss","Components.Bind.IsInvariantCulture":"True","Components.Bind.TypeAttribute":"datetime-local","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"input","Attributes":[{"Name":"type","Value":"datetime-local","ValueComparison":1},{"Name":"@bind","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"input","Attributes":[{"Name":"type","Value":"datetime-local","ValueComparison":1},{"Name":"@bind:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":"yyyy-MM-ddTHH:mm:ss","Components.Bind.IsInvariantCulture":"True","Components.Bind.TypeAttribute":"datetime-local","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind_value","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"input","Attributes":[{"Name":"type","Value":"month","ValueComparison":1},{"Name":"@bind-value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"input","Attributes":[{"Name":"type","Value":"month","ValueComparison":1},{"Name":"@bind-value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-value","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind_value"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind-value' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":"yyyy-MM","Components.Bind.IsInvariantCulture":"True","Components.Bind.TypeAttribute":"month","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"input","Attributes":[{"Name":"type","Value":"month","ValueComparison":1},{"Name":"@bind","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"input","Attributes":[{"Name":"type","Value":"month","ValueComparison":1},{"Name":"@bind:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":"yyyy-MM","Components.Bind.IsInvariantCulture":"True","Components.Bind.TypeAttribute":"month","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind_value","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"input","Attributes":[{"Name":"type","Value":"number","ValueComparison":1},{"Name":"@bind-value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"input","Attributes":[{"Name":"type","Value":"number","ValueComparison":1},{"Name":"@bind-value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-value","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind_value"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind-value' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":null,"Components.Bind.IsInvariantCulture":"True","Components.Bind.TypeAttribute":"number","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"input","Attributes":[{"Name":"type","Value":"number","ValueComparison":1},{"Name":"@bind","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"input","Attributes":[{"Name":"type","Value":"number","ValueComparison":1},{"Name":"@bind:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":null,"Components.Bind.IsInvariantCulture":"True","Components.Bind.TypeAttribute":"number","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"input","Attributes":[{"Name":"type","Value":"text","ValueComparison":1},{"Name":"@bind","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"input","Attributes":[{"Name":"type","Value":"text","ValueComparison":1},{"Name":"@bind:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":null,"Components.Bind.IsInvariantCulture":"False","Components.Bind.TypeAttribute":"text","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind_value","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"input","Attributes":[{"Name":"type","Value":"time","ValueComparison":1},{"Name":"@bind-value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"input","Attributes":[{"Name":"type","Value":"time","ValueComparison":1},{"Name":"@bind-value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-value","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind_value"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind-value' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":"HH:mm:ss","Components.Bind.IsInvariantCulture":"True","Components.Bind.TypeAttribute":"time","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"input","Attributes":[{"Name":"type","Value":"time","ValueComparison":1},{"Name":"@bind","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"input","Attributes":[{"Name":"type","Value":"time","ValueComparison":1},{"Name":"@bind:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":"HH:mm:ss","Components.Bind.IsInvariantCulture":"True","Components.Bind.TypeAttribute":"time","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind_value","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"input","Attributes":[{"Name":"@bind-value","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"input","Attributes":[{"Name":"@bind-value:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind-value:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind-value","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind_value"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind-value' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":null,"Components.Bind.IsInvariantCulture":"False","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Bind","Name":"Bind","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"input","Attributes":[{"Name":"@bind","Metadata":{"Common.DirectiveAttribute":"True"}}]},{"TagName":"input","Attributes":[{"Name":"@bind:get","Metadata":{"Common.DirectiveAttribute":"True"}},{"Name":"@bind:set","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Bind","Name":"@bind","TypeName":"System.Object","Documentation":"Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Bind"},"Parameters":[{"Name":"format","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}},{"Name":"event","TypeName":"System.String","Documentation":"Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.","Metadata":{"Common.PropertyName":"Event_value"}},{"Name":"culture","TypeName":"System.Globalization.CultureInfo","Documentation":"Specifies the culture to use for conversions.","Metadata":{"Common.PropertyName":"Culture"}},{"Name":"get","TypeName":"System.Object","Documentation":"Specifies the expression to use for binding the value to the attribute.","Metadata":{"Common.PropertyName":"Get","Components.Bind.AlternativeNotation":"True"}},{"Name":"set","TypeName":"System.Delegate","Documentation":"Specifies the expression to use for updating the bound value when a new value is available.","Metadata":{"Common.PropertyName":"Set"}},{"Name":"after","TypeName":"System.Delegate","Documentation":"Specifies an action to run after the new value has been set.","Metadata":{"Common.PropertyName":"After"}}]},{"Kind":"Components.Bind","Name":"format-value","TypeName":"System.String","Documentation":"Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.","Metadata":{"Common.PropertyName":"Format_value"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Web.BindAttributes","Common.TypeNameIdentifier":"BindAttributes","Common.TypeNamespace":"Microsoft.AspNetCore.Components.Web","Components.Bind.ChangeAttribute":"onchange","Components.Bind.Format":null,"Components.Bind.IsInvariantCulture":"False","Components.Bind.ValueAttribute":"value","Components.IsSpecialKind":"Components.Bind","Runtime.Name":"Components.None"}},{"Kind":"Components.Ref","Name":"Ref","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Populates the specified field or property with a reference to the element or component.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@ref","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Ref","Name":"@ref","TypeName":"System.Object","Documentation":"Populates the specified field or property with a reference to the element or component.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Ref"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Ref","Components.IsSpecialKind":"Components.Ref","Runtime.Name":"Components.None"}},{"Kind":"Components.Key","Name":"Key","AssemblyName":"Microsoft.AspNetCore.Components","Documentation":"Ensures that the component or element will be preserved across renders if (and only if) the supplied key value matches.","CaseSensitive":true,"TagMatchingRules":[{"TagName":"*","Attributes":[{"Name":"@key","Metadata":{"Common.DirectiveAttribute":"True"}}]}],"BoundAttributes":[{"Kind":"Components.Key","Name":"@key","TypeName":"System.Object","Documentation":"Ensures that the component or element will be preserved across renders if (and only if) the supplied key value matches.","Metadata":{"Common.DirectiveAttribute":"True","Common.PropertyName":"Key"}}],"Metadata":{"Common.ClassifyAttributesOnly":"True","Common.TypeName":"Microsoft.AspNetCore.Components.Key","Components.IsSpecialKind":"Components.Key","Runtime.Name":"Components.None"}}]
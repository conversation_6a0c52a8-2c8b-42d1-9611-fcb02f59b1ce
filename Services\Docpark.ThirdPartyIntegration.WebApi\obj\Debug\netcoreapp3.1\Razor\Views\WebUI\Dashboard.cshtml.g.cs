#pragma checksum "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\WebUI\Dashboard.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "26ea8d8817ea50ecb5ae137f0d1ec1dcaad2cc37d73b93f4435ecb45af08320f"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_WebUI_Dashboard), @"mvc.1.0.view", @"/Views/WebUI/Dashboard.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.WebApi

#nullable disable
    ;
#nullable restore
#line 2 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.Domain.Entities

#nullable disable
    ;
#nullable restore
#line 3 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.Services.Interfaces

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"26ea8d8817ea50ecb5ae137f0d1ec1dcaad2cc37d73b93f4435ecb45af08320f", @"/Views/WebUI/Dashboard.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"ac12188ef915a0efb6909a3d05593257fa18235f65c65f2698e4c97193ba4524", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_WebUI_Dashboard : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\WebUI\Dashboard.cshtml"
  
    Layout = "_Layout";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<!-- 警告容器 -->
<div id=""alerts-container""></div>

<!-- 统计卡片 -->
<div class=""row mb-4"">
    <div class=""col-xl-3 col-md-6 mb-4"">
        <div class=""card border-left-primary shadow h-100 py-2"">
            <div class=""card-body"">
                <div class=""row no-gutters align-items-center"">
                    <div class=""col mr-2"">
                        <div class=""text-xs font-weight-bold text-primary text-uppercase mb-1"">
                            API配置总数
                        </div>
                        <div class=""h5 mb-0 font-weight-bold text-gray-800"" id=""total-apis"">
                            <i class=""fas fa-spinner fa-spin""></i>
                        </div>
                    </div>
                    <div class=""col-auto"">
                        <i class=""fas fa-cogs fa-2x text-gray-300""></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class=""col-xl-3 col-md-6 mb-4"">
        <div class=""card border-left-success shadow h-1");
            WriteLiteral(@"00 py-2"">
            <div class=""card-body"">
                <div class=""row no-gutters align-items-center"">
                    <div class=""col mr-2"">
                        <div class=""text-xs font-weight-bold text-success text-uppercase mb-1"">
                            活跃任务数
                        </div>
                        <div class=""h5 mb-0 font-weight-bold text-gray-800"" id=""active-jobs"">
                            <i class=""fas fa-spinner fa-spin""></i>
                        </div>
                    </div>
                    <div class=""col-auto"">
                        <i class=""fas fa-clock fa-2x text-gray-300""></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class=""col-xl-3 col-md-6 mb-4"">
        <div class=""card border-left-info shadow h-100 py-2"">
            <div class=""card-body"">
                <div class=""row no-gutters align-items-center"">
                    <div class=""col mr-2"">
                        <div clas");
            WriteLiteral(@"s=""text-xs font-weight-bold text-info text-uppercase mb-1"">
                            今日执行次数
                        </div>
                        <div class=""h5 mb-0 font-weight-bold text-gray-800"" id=""today-executions"">
                            <i class=""fas fa-spinner fa-spin""></i>
                        </div>
                    </div>
                    <div class=""col-auto"">
                        <i class=""fas fa-play fa-2x text-gray-300""></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class=""col-xl-3 col-md-6 mb-4"">
        <div class=""card border-left-warning shadow h-100 py-2"">
            <div class=""card-body"">
                <div class=""row no-gutters align-items-center"">
                    <div class=""col mr-2"">
                        <div class=""text-xs font-weight-bold text-warning text-uppercase mb-1"">
                            成功率
                        </div>
                        <div class=""h5 mb-0 font-weight");
            WriteLiteral(@"-bold text-gray-800"" id=""success-rate"">
                            <i class=""fas fa-spinner fa-spin""></i>
                        </div>
                    </div>
                    <div class=""col-auto"">
                        <i class=""fas fa-chart-pie fa-2x text-gray-300""></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表和列表 -->
<div class=""row"">
    <!-- 执行趋势图表 -->
    <div class=""col-xl-8 col-lg-7"">
        <div class=""card shadow mb-4"">
            <div class=""card-header py-3 d-flex flex-row align-items-center justify-content-between"">
                <h6 class=""m-0 font-weight-bold text-primary"">执行趋势</h6>
                <div class=""dropdown no-arrow"">
                    <a class=""dropdown-toggle"" href=""#"" role=""button"" id=""dropdownMenuLink"" data-bs-toggle=""dropdown"">
                        <i class=""fas fa-ellipsis-v fa-sm fa-fw text-gray-400""></i>
                    </a>
                    <div class=""dropdown-menu dropdown-men");
            WriteLiteral(@"u-right shadow"">
                        <a class=""dropdown-item"" href=""#"" onclick=""refreshChart()"">刷新数据</a>
                        <a class=""dropdown-item"" href=""/execution-history"">查看详情</a>
                    </div>
                </div>
            </div>
            <div class=""card-body"">
                <div class=""chart-area"">
                    <canvas id=""executionChart""></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近执行 -->
    <div class=""col-xl-4 col-lg-5"">
        <div class=""card shadow mb-4"">
            <div class=""card-header py-3"">
                <h6 class=""m-0 font-weight-bold text-primary"">最近执行</h6>
            </div>
            <div class=""card-body"">
                <div id=""recent-executions"">
                    <div class=""text-center"">
                        <i class=""fas fa-spinner fa-spin""></i>
                        <p class=""mt-2"">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </");
            WriteLiteral(@"div>
</div>

<!-- 系统状态 -->
<div class=""row"">
    <div class=""col-lg-6 mb-4"">
        <div class=""card shadow"">
            <div class=""card-header py-3"">
                <h6 class=""m-0 font-weight-bold text-primary"">调度器状态</h6>
            </div>
            <div class=""card-body"">
                <div id=""scheduler-status"">
                    <div class=""text-center"">
                        <i class=""fas fa-spinner fa-spin""></i>
                        <p class=""mt-2"">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class=""col-lg-6 mb-4"">
        <div class=""card shadow"">
            <div class=""card-header py-3"">
                <h6 class=""m-0 font-weight-bold text-primary"">快速操作</h6>
            </div>
            <div class=""card-body"">
                <div class=""d-grid gap-2"">
                    <a href=""/api-configs"" class=""btn btn-primary"">
                        <i class=""fas fa-plus me-2""></i>创建新API配置
                    </a>
     ");
            WriteLiteral(@"               <a href=""/api-test"" class=""btn btn-success"">
                        <i class=""fas fa-flask me-2""></i>测试API接口
                    </a>
                    <button class=""btn btn-warning"" onclick=""startScheduler()"">
                        <i class=""fas fa-play me-2""></i>启动调度器
                    </button>
                    <a href=""/swagger"" class=""btn btn-info"" target=""_blank"">
                        <i class=""fas fa-book me-2""></i>查看API文档
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

");
            DefineSection("Scripts", async() => {
                WriteLiteral(@"
<script>
    let executionChart;

    $(document).ready(function() {
        loadDashboardData();
        initExecutionChart();
        
        // 每30秒刷新一次数据
        setInterval(loadDashboardData, 30000);
    });

    function loadDashboardData() {
        // 加载统计数据
        loadStatistics();
        loadRecentExecutions();
        loadSchedulerStatus();
    }

    function loadStatistics() {
        // 加载API配置总数
        callAPI('/api/ApiConfiguration')
            .done(function(data) {
                $('#total-apis').text(data.length || 0);
            })
            .fail(function() {
                $('#total-apis').text('N/A');
            });

        // 加载调度器统计
        callAPI('/api/Scheduler/statistics')
            .done(function(data) {
                $('#active-jobs').text(data.runningJobs || 0);
                $('#today-executions').text(data.todayExecutions || 0);
                
                var successRate = data.todayExecutions > 0 
                    ? ((data.todaySuccesses / data.to");
                WriteLiteral(@"dayExecutions) * 100).toFixed(1) + '%'
                    : 'N/A';
                $('#success-rate').text(successRate);
            })
            .fail(function() {
                $('#active-jobs').text('N/A');
                $('#today-executions').text('N/A');
                $('#success-rate').text('N/A');
            });
    }

    function loadRecentExecutions() {
        // 这里应该调用获取最近执行记录的API
        // 暂时显示示例数据
        var html = `
            <div class=""d-flex align-items-center mb-3"">
                <div class=""me-3"">
                    <span class=""badge bg-success"">成功</span>
                </div>
                <div class=""flex-grow-1"">
                    <div class=""small text-gray-500"">2分钟前</div>
                    <div>用户数据同步</div>
                </div>
            </div>
            <div class=""d-flex align-items-center mb-3"">
                <div class=""me-3"">
                    <span class=""badge bg-danger"">失败</span>
                </div>
                <div class=""flex-grow-1""");
                WriteLiteral(@">
                    <div class=""small text-gray-500"">5分钟前</div>
                    <div>订单状态更新</div>
                </div>
            </div>
            <div class=""d-flex align-items-center"">
                <div class=""me-3"">
                    <span class=""badge bg-success"">成功</span>
                </div>
                <div class=""flex-grow-1"">
                    <div class=""small text-gray-500"">10分钟前</div>
                    <div>库存数据获取</div>
                </div>
            </div>
        `;
        $('#recent-executions').html(html);
    }

    function loadSchedulerStatus() {
        callAPI('/api/Scheduler/statistics')
            .done(function(data) {
                var html = `
                    <div class=""row"">
                        <div class=""col-6"">
                            <strong>调度器名称:</strong><br>
                            <span class=""text-muted"">${data.schedulerName || 'N/A'}</span>
                        </div>
                        <div class=""col-6"">
        ");
                WriteLiteral(@"                    <strong>状态:</strong><br>
                            <span class=""badge bg-success"">${data.schedulerState || '未知'}</span>
                        </div>
                    </div>
                    <hr>
                    <div class=""row"">
                        <div class=""col-6"">
                            <strong>总任务数:</strong><br>
                            <span class=""text-muted"">${data.totalJobs || 0}</span>
                        </div>
                        <div class=""col-6"">
                            <strong>运行中:</strong><br>
                            <span class=""text-muted"">${data.runningJobs || 0}</span>
                        </div>
                    </div>
                `;
                $('#scheduler-status').html(html);
            })
            .fail(function() {
                $('#scheduler-status').html('<div class=""text-danger"">加载失败</div>');
            });
    }

    function initExecutionChart() {
        var ctx = document.getElementById('execu");
                WriteLiteral(@"tionChart').getContext('2d');
        executionChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['6小时前', '5小时前', '4小时前', '3小时前', '2小时前', '1小时前', '现在'],
                datasets: [{
                    label: '成功执行',
                    data: [12, 19, 15, 25, 22, 18, 24],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.1
                }, {
                    label: '失败执行',
                    data: [2, 3, 1, 2, 1, 0, 1],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }");
                WriteLiteral(@"

    function refreshChart() {
        // 刷新图表数据
        showSuccess('图表数据已刷新');
    }

    function startScheduler() {
        callAPI('/api/Scheduler/start', 'POST')
            .done(function(data) {
                showSuccess('调度器启动成功');
                loadSchedulerStatus();
            })
            .fail(function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '启动失败';
                showError('调度器启动失败: ' + message);
            });
    }
</script>
");
            }
            );
            WriteLiteral(@"
<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .chart-area {
        position: relative;
        height: 300px;
    }
</style>
");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591

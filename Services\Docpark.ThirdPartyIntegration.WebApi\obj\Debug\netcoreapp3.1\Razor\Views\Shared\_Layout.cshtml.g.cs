#pragma checksum "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\Shared\_Layout.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "8d11960bfb6b0427e6a478b05afcd649ca6f17d0a0fbc66f67be17b39a97e500"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Shared__Layout), @"mvc.1.0.view", @"/Views/Shared/_Layout.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.WebApi

#nullable disable
    ;
#nullable restore
#line 2 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.Domain.Entities

#nullable disable
    ;
#nullable restore
#line 3 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.Services.Interfaces

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"8d11960bfb6b0427e6a478b05afcd649ca6f17d0a0fbc66f67be17b39a97e500", @"/Views/Shared/_Layout.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"ac12188ef915a0efb6909a3d05593257fa18235f65c65f2698e4c97193ba4524", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Shared__Layout : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
            WriteLiteral("<!DOCTYPE html>\n<html lang=\"zh-CN\">\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "8d11960bfb6b0427e6a478b05afcd649ca6f17d0a0fbc66f67be17b39a97e5003939", async() => {
                WriteLiteral("\n    <meta charset=\"utf-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>");
                Write(
#nullable restore
#line 6 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\Shared\_Layout.cshtml"
            ViewBag.Title

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@" - 第三方接口集成管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href=""https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"" rel=""stylesheet"">
    <!-- Font Awesome -->
    <link href=""https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"" rel=""stylesheet"">
    <!-- Custom CSS -->
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25re");
                WriteLiteral("m rgba(0, 0, 0, 0.075);\n        }\n        .navbar-brand {\n            font-weight: bold;\n        }\n        .status-badge {\n            font-size: 0.75rem;\n        }\n    </style>\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "8d11960bfb6b0427e6a478b05afcd649ca6f17d0a0fbc66f67be17b39a97e5006626", async() => {
                WriteLiteral(@"
    <div class=""container-fluid"">
        <div class=""row"">
            <!-- 侧边栏 -->
            <nav class=""col-md-3 col-lg-2 d-md-block sidebar collapse"">
                <div class=""position-sticky pt-3"">
                    <div class=""text-center mb-4"">
                        <h5 class=""text-white"">
                            <i class=""fas fa-plug me-2""></i>
                            接口集成系统
                        </h5>
                    </div>
                    
                    <ul class=""nav flex-column"">
                        <li class=""nav-item"">
                            <a class=""nav-link"" href=""/dashboard"">
                                <i class=""fas fa-tachometer-alt me-2""></i>
                                仪表板
                            </a>
                        </li>
                        <li class=""nav-item"">
                            <a class=""nav-link"" href=""/api-configs"">
                                <i class=""fas fa-cogs me-2""></i>
                          ");
                WriteLiteral(@"      API配置
                            </a>
                        </li>
                        <li class=""nav-item"">
                            <a class=""nav-link"" href=""/data-mapping"">
                                <i class=""fas fa-exchange-alt me-2""></i>
                                数据映射
                            </a>
                        </li>
                        <li class=""nav-item"">
                            <a class=""nav-link"" href=""/scheduler"">
                                <i class=""fas fa-clock me-2""></i>
                                任务调度
                            </a>
                        </li>
                        <li class=""nav-item"">
                            <a class=""nav-link"" href=""/execution-history"">
                                <i class=""fas fa-history me-2""></i>
                                执行历史
                            </a>
                        </li>
                        <li class=""nav-item"">
                            <a class=""nav-link");
                WriteLiteral(@""" href=""/monitoring"">
                                <i class=""fas fa-chart-line me-2""></i>
                                系统监控
                            </a>
                        </li>
                        <li class=""nav-item"">
                            <a class=""nav-link"" href=""/api-test"">
                                <i class=""fas fa-flask me-2""></i>
                                API测试
                            </a>
                        </li>
                        <li class=""nav-item"">
                            <a class=""nav-link"" href=""/settings"">
                                <i class=""fas fa-sliders-h me-2""></i>
                                系统设置
                            </a>
                        </li>
                    </ul>
                    
                    <hr class=""my-3"">
                    
                    <div class=""text-center"">
                        <small class=""text-white-50"">
                            <i class=""fas fa-info-circle me-1""><");
                WriteLiteral(@"/i>
                            版本 2.0.0
                        </small>
                    </div>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class=""col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content"">
                <!-- 顶部导航栏 -->
                <div class=""d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"">
                    <h1 class=""h2"">");
                Write(
#nullable restore
#line 125 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\Shared\_Layout.cshtml"
                                    ViewBag.Title

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</h1>
                    <div class=""btn-toolbar mb-2 mb-md-0"">
                        <div class=""btn-group me-2"">
                            <button type=""button"" class=""btn btn-sm btn-outline-secondary"" onclick=""location.reload()"">
                                <i class=""fas fa-sync-alt me-1""></i>
                                刷新
                            </button>
                            <a href=""/swagger"" class=""btn btn-sm btn-outline-primary"" target=""_blank"">
                                <i class=""fas fa-book me-1""></i>
                                API文档
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 页面内容 -->
                ");
                Write(
#nullable restore
#line 141 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\Shared\_Layout.cshtml"
                 RenderBody()

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src=""https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js""></script>
    <!-- jQuery -->
    <script src=""https://code.jquery.com/jquery-3.6.0.min.js""></script>
    <!-- Chart.js -->
    <script src=""https://cdn.jsdelivr.net/npm/chart.js""></script>
    
    <!-- 通用JavaScript -->
    <script>
        // 设置当前页面的导航链接为活动状态
        $(document).ready(function() {
            var currentPath = window.location.pathname;
            $('.sidebar .nav-link').each(function() {
                if ($(this).attr('href') === currentPath) {
                    $(this).addClass('active');
                }
            });
        });

        // 通用API调用函数
        function callAPI(url, method = 'GET', data = null) {
            return $.ajax({
                url: url,
                method: method,
                data: data ? JSON.stringify(data) : null,
                contentType: 'application/json',
              ");
                WriteLiteral(@"  dataType: 'json'
            });
        }

        // 显示成功消息
        function showSuccess(message) {
            showAlert(message, 'success');
        }

        // 显示错误消息
        function showError(message) {
            showAlert(message, 'danger');
        }

        // 显示警告消息
        function showWarning(message) {
            showAlert(message, 'warning');
        }

        // 显示通用警告框
        function showAlert(message, type) {
            var alertHtml = `
                <div class=""alert alert-${type} alert-dismissible fade show"" role=""alert"">
                    ${message}
                    <button type=""button"" class=""btn-close"" data-bs-dismiss=""alert""></button>
                </div>
            `;
            $('#alerts-container').html(alertHtml);
            
            // 3秒后自动隐藏
            setTimeout(function() {
                $('.alert').alert('close');
            }, 3000);
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) ret");
                WriteLiteral(@"urn '-';
            var date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            var k = 1024;
            var sizes = ['Bytes', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>

    ");
                Write(
#nullable restore
#line 224 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\Shared\_Layout.cshtml"
     RenderSection("Scripts", required: false)

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n</html>\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591

#pragma checksum "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\WebUI\ExecutionHistory.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "271519f022b00fb0ac82c6000088c9a0077fd830ac82280c993bb959d8bc55fa"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_WebUI_ExecutionHistory), @"mvc.1.0.view", @"/Views/WebUI/ExecutionHistory.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.WebApi

#nullable disable
    ;
#nullable restore
#line 2 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.Domain.Entities

#nullable disable
    ;
#nullable restore
#line 3 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.Services.Interfaces

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"271519f022b00fb0ac82c6000088c9a0077fd830ac82280c993bb959d8bc55fa", @"/Views/WebUI/ExecutionHistory.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"ac12188ef915a0efb6909a3d05593257fa18235f65c65f2698e4c97193ba4524", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_WebUI_ExecutionHistory : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "Success", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "Failed", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "Running", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\WebUI\ExecutionHistory.cshtml"
  
    Layout = "_Layout";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<!-- 警告容器 -->
<div id=""alerts-container""></div>

<!-- 筛选器 -->
<div class=""row mb-4"">
    <div class=""col-12"">
        <div class=""card"">
            <div class=""card-header"">
                <h6 class=""m-0 font-weight-bold text-primary"">筛选条件</h6>
            </div>
            <div class=""card-body"">
                <div class=""row"">
                    <div class=""col-md-3"">
                        <label for=""jobKeyFilter"" class=""form-label"">任务键</label>
                        <select class=""form-select"" id=""jobKeyFilter"">
                            ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "271519f022b00fb0ac82c6000088c9a0077fd830ac82280c993bb959d8bc55fa5810", async() => {
                WriteLiteral("全部任务");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
                        </select>
                    </div>
                    <div class=""col-md-3"">
                        <label for=""statusFilter"" class=""form-label"">执行状态</label>
                        <select class=""form-select"" id=""statusFilter"">
                            ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "271519f022b00fb0ac82c6000088c9a0077fd830ac82280c993bb959d8bc55fa7284", async() => {
                WriteLiteral("全部状态");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                            ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "271519f022b00fb0ac82c6000088c9a0077fd830ac82280c993bb959d8bc55fa8491", async() => {
                WriteLiteral("成功");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_1.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_1);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                            ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "271519f022b00fb0ac82c6000088c9a0077fd830ac82280c993bb959d8bc55fa9696", async() => {
                WriteLiteral("失败");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_2.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_2);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n                            ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "271519f022b00fb0ac82c6000088c9a0077fd830ac82280c993bb959d8bc55fa10901", async() => {
                WriteLiteral("运行中");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_3.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_3);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
                        </select>
                    </div>
                    <div class=""col-md-3"">
                        <label for=""startDateFilter"" class=""form-label"">开始时间</label>
                        <input type=""datetime-local"" class=""form-control"" id=""startDateFilter"">
                    </div>
                    <div class=""col-md-3"">
                        <label for=""endDateFilter"" class=""form-label"">结束时间</label>
                        <input type=""datetime-local"" class=""form-control"" id=""endDateFilter"">
                    </div>
                </div>
                <div class=""row mt-3"">
                    <div class=""col-12"">
                        <button class=""btn btn-primary"" onclick=""loadExecutionHistory()"">
                            <i class=""fas fa-search me-2""></i>查询
                        </button>
                        <button class=""btn btn-secondary ms-2"" onclick=""resetFilters()"">
                            <i class=""fas fa-undo me-2""></i>重置
                    ");
            WriteLiteral(@"    </button>
                        <button class=""btn btn-success ms-2"" onclick=""exportHistory()"">
                            <i class=""fas fa-download me-2""></i>导出
                        </button>
                        <button class=""btn btn-danger ms-2"" onclick=""cleanupHistory()"">
                            <i class=""fas fa-trash me-2""></i>清理历史
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 执行历史列表 -->
<div class=""row"">
    <div class=""col-12"">
        <div class=""card shadow"">
            <div class=""card-header py-3"">
                <h6 class=""m-0 font-weight-bold text-primary"">执行历史记录</h6>
            </div>
            <div class=""card-body"">
                <div class=""table-responsive"">
                    <table class=""table table-bordered"" id=""executionHistoryTable"" width=""100%"" cellspacing=""0"">
                        <thead>
                            <tr>
                                <th>任务键<");
            WriteLiteral(@"/th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>执行状态</th>
                                <th>耗时(秒)</th>
                                <th>错误信息</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id=""executionHistoryTableBody"">
                            <tr>
                                <td colspan=""7"" class=""text-center"">
                                    <i class=""fas fa-spinner fa-spin""></i>
                                    <span class=""ms-2"">加载中...</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label=""执行历史分页"">
                    <ul class=""pagination justify-content-center"" id=""pagination"">
                        <!-- 分页按钮将通过JavaS");
            WriteLiteral(@"cript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 执行详情模态框 -->
<div class=""modal fade"" id=""executionDetailModal"" tabindex=""-1"" aria-labelledby=""executionDetailModalLabel"" aria-hidden=""true"">
    <div class=""modal-dialog modal-lg"">
        <div class=""modal-content"">
            <div class=""modal-header"">
                <h5 class=""modal-title"" id=""executionDetailModalLabel"">执行详情</h5>
                <button type=""button"" class=""btn-close"" data-bs-dismiss=""modal"" aria-label=""Close""></button>
            </div>
            <div class=""modal-body"" id=""executionDetailContent"">
                <!-- 详情内容将通过JavaScript动态加载 -->
            </div>
            <div class=""modal-footer"">
                <button type=""button"" class=""btn btn-secondary"" data-bs-dismiss=""modal"">关闭</button>
            </div>
        </div>
    </div>
</div>

");
            DefineSection("Scripts", async() => {
                WriteLiteral(@"
<script>
    let currentPage = 1;
    let pageSize = 20;
    let totalPages = 1;

    $(document).ready(function() {
        loadJobKeys();
        loadExecutionHistory();
    });

    function loadJobKeys() {
        callAPI('/api/Scheduler/jobs')
            .done(function(data) {
                var select = $('#jobKeyFilter');
                select.empty().append('<option value="""">全部任务</option>');
                
                if (data && data.length > 0) {
                    data.forEach(function(job) {
                        select.append('<option value=""' + job.key + '"">' + job.key + '</option>');
                    });
                }
            })
            .fail(function() {
                console.error('加载任务列表失败');
            });
    }

    function loadExecutionHistory(page = 1) {
        currentPage = page;
        
        var params = {
            page: currentPage,
            pageSize: pageSize,
            jobKey: $('#jobKeyFilter').val(),
            status: $('#statusFilter");
                WriteLiteral(@"').val(),
            startDate: $('#startDateFilter').val(),
            endDate: $('#endDateFilter').val()
        };

        // 移除空值参数
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key];
            }
        });

        var queryString = $.param(params);
        var url = '/api/Scheduler/execution-history' + (queryString ? '?' + queryString : '');

        callAPI(url)
            .done(function(data) {
                displayExecutionHistory(data.items || data);
                updatePagination(data.totalPages || 1, data.currentPage || 1);
            })
            .fail(function() {
                $('#executionHistoryTableBody').html('<tr><td colspan=""7"" class=""text-center text-danger"">加载失败</td></tr>');
            });
    }

    function displayExecutionHistory(histories) {
        var tbody = $('#executionHistoryTableBody');
        tbody.empty();
        
        if (!histories ");
                WriteLiteral(@"|| histories.length === 0) {
            tbody.html('<tr><td colspan=""7"" class=""text-center text-muted"">暂无执行历史记录</td></tr>');
            return;
        }

        histories.forEach(function(history) {
            var statusBadge = getStatusBadge(history.status);
            var duration = history.endTime && history.startTime ? 
                ((new Date(history.endTime) - new Date(history.startTime)) / 1000).toFixed(2) : '-';
            
            var row = `
                <tr>
                    <td>${history.jobKey || '-'}</td>
                    <td>${formatDateTime(history.startTime)}</td>
                    <td>${formatDateTime(history.endTime)}</td>
                    <td>${statusBadge}</td>
                    <td>${duration}</td>
                    <td class=""text-truncate"" style=""max-width: 200px;"" title=""${history.errorMessage || ''}"">${history.errorMessage || '-'}</td>
                    <td>
                        <button class=""btn btn-sm btn-outline-primary"" onclick=""showExecution");
                WriteLiteral(@"Detail('${history.id}')"">
                            <i class=""fas fa-eye""></i> 详情
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    function getStatusBadge(status) {
        switch (status) {
            case 'Success':
                return '<span class=""badge bg-success"">成功</span>';
            case 'Failed':
                return '<span class=""badge bg-danger"">失败</span>';
            case 'Running':
                return '<span class=""badge bg-primary"">运行中</span>';
            default:
                return '<span class=""badge bg-secondary"">未知</span>';
        }
    }

    function formatDateTime(dateTime) {
        if (!dateTime) return '-';
        return new Date(dateTime).toLocaleString('zh-CN');
    }

    function updatePagination(total, current) {
        totalPages = total;
        currentPage = current;
        
        var pagination = $('#pagination');
        pagination.empty();

        i");
                WriteLiteral(@"f (totalPages <= 1) return;

        // 上一页
        var prevDisabled = currentPage === 1 ? 'disabled' : '';
        pagination.append(`
            <li class=""page-item ${prevDisabled}"">
                <a class=""page-link"" href=""#"" onclick=""loadExecutionHistory(${currentPage - 1})"">上一页</a>
            </li>
        `);

        // 页码
        var startPage = Math.max(1, currentPage - 2);
        var endPage = Math.min(totalPages, currentPage + 2);

        for (var i = startPage; i <= endPage; i++) {
            var active = i === currentPage ? 'active' : '';
            pagination.append(`
                <li class=""page-item ${active}"">
                    <a class=""page-link"" href=""#"" onclick=""loadExecutionHistory(${i})"">${i}</a>
                </li>
            `);
        }

        // 下一页
        var nextDisabled = currentPage === totalPages ? 'disabled' : '';
        pagination.append(`
            <li class=""page-item ${nextDisabled}"">
                <a class=""page-link"" href=""#"" onclick=""loadExecut");
                WriteLiteral(@"ionHistory(${currentPage + 1})"">下一页</a>
            </li>
        `);
    }

    function showExecutionDetail(executionId) {
        callAPI('/api/Scheduler/execution-detail/' + executionId)
            .done(function(data) {
                var content = `
                    <div class=""row"">
                        <div class=""col-md-6"">
                            <strong>任务键:</strong> ${data.jobKey || '-'}
                        </div>
                        <div class=""col-md-6"">
                            <strong>执行状态:</strong> ${getStatusBadge(data.status)}
                        </div>
                    </div>
                    <div class=""row mt-2"">
                        <div class=""col-md-6"">
                            <strong>开始时间:</strong> ${formatDateTime(data.startTime)}
                        </div>
                        <div class=""col-md-6"">
                            <strong>结束时间:</strong> ${formatDateTime(data.endTime)}
                        </div>
                    </di");
                WriteLiteral(@"v>
                    <div class=""row mt-2"">
                        <div class=""col-12"">
                            <strong>错误信息:</strong>
                            <pre class=""mt-2 p-2 bg-light border rounded"">${data.errorMessage || '无'}</pre>
                        </div>
                    </div>
                    <div class=""row mt-2"">
                        <div class=""col-12"">
                            <strong>执行日志:</strong>
                            <pre class=""mt-2 p-2 bg-light border rounded"" style=""max-height: 300px; overflow-y: auto;"">${data.logs || '无日志记录'}</pre>
                        </div>
                    </div>
                `;
                $('#executionDetailContent').html(content);
                $('#executionDetailModal').modal('show');
            })
            .fail(function() {
                showError('加载执行详情失败');
            });
    }

    function resetFilters() {
        $('#jobKeyFilter').val('');
        $('#statusFilter').val('');
        $('#startDateFi");
                WriteLiteral(@"lter').val('');
        $('#endDateFilter').val('');
        loadExecutionHistory();
    }

    function exportHistory() {
        var params = {
            jobKey: $('#jobKeyFilter').val(),
            status: $('#statusFilter').val(),
            startDate: $('#startDateFilter').val(),
            endDate: $('#endDateFilter').val(),
            export: true
        };

        // 移除空值参数
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key];
            }
        });

        var queryString = $.param(params);
        var url = '/api/Scheduler/execution-history/export' + (queryString ? '?' + queryString : '');
        
        // 创建下载链接
        var link = document.createElement('a');
        link.href = url;
        link.download = 'execution_history_' + new Date().toISOString().slice(0, 10) + '.csv';
        document.body.appendChild(link);
        link.click();
        document.body.removeC");
                WriteLiteral(@"hild(link);
    }

    function cleanupHistory() {
        var days = prompt('请输入要保留的天数（将删除更早的记录）:', '30');
        if (days === null || days === '') return;
        
        days = parseInt(days);
        if (isNaN(days) || days < 1) {
            showError('请输入有效的天数');
            return;
        }

        if (!confirm(`确定要删除 ${days} 天前的执行历史记录吗？此操作不可恢复。`)) {
            return;
        }

        callAPI('/api/Scheduler/cleanup-history?olderThanDays=' + days, 'DELETE')
            .done(function(result) {
                showSuccess(`清理完成，删除了 ${result.deletedCount} 条记录`);
                loadExecutionHistory();
            })
            .fail(function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '清理失败';
                showError('清理失败: ' + message);
            });
    }
</script>
");
            }
            );
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591

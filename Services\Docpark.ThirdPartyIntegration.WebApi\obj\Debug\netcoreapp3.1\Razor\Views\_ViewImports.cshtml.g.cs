#pragma checksum "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "ac12188ef915a0efb6909a3d05593257fa18235f65c65f2698e4c97193ba4524"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views__ViewImports), @"mvc.1.0.view", @"/Views/_ViewImports.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
 using Docpark.ThirdPartyIntegration.WebApi

#nullable disable
    ;
#nullable restore
#line 2 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
 using Docpark.ThirdPartyIntegration.Domain.Entities

#nullable disable
    ;
#nullable restore
#line 3 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
 using Docpark.ThirdPartyIntegration.Services.Interfaces

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"ac12188ef915a0efb6909a3d05593257fa18235f65c65f2698e4c97193ba4524", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views__ViewImports : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591

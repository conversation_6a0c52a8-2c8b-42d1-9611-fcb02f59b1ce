#pragma checksum "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\WebUI\ApiTest.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "2ba98b6e934b9ba94deea0d1b82ecc778fc793aa4243858a127c14a5f530be3e"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_WebUI_ApiTest), @"mvc.1.0.view", @"/Views/WebUI/ApiTest.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.WebApi

#nullable disable
    ;
#nullable restore
#line 2 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.Domain.Entities

#nullable disable
    ;
#nullable restore
#line 3 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\_ViewImports.cshtml"
using Docpark.ThirdPartyIntegration.Services.Interfaces

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"2ba98b6e934b9ba94deea0d1b82ecc778fc793aa4243858a127c14a5f530be3e", @"/Views/WebUI/ApiTest.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"ac12188ef915a0efb6909a3d05593257fa18235f65c65f2698e4c97193ba4524", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_WebUI_ApiTest : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\Views\WebUI\ApiTest.cshtml"
  
    Layout = "_Layout";

#line default
#line hidden
#nullable disable

            WriteLiteral(@"
<!-- 警告容器 -->
<div id=""alerts-container""></div>

<!-- API选择和测试控制 -->
<div class=""row mb-4"">
    <div class=""col-12"">
        <div class=""card"">
            <div class=""card-header"">
                <h6 class=""m-0 font-weight-bold text-primary"">API测试工具</h6>
            </div>
            <div class=""card-body"">
                <div class=""row"">
                    <div class=""col-md-6"">
                        <label for=""apiSelect"" class=""form-label"">选择API配置</label>
                        <select class=""form-select"" id=""apiSelect"">
                            ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2ba98b6e934b9ba94deea0d1b82ecc778fc793aa4243858a127c14a5f530be3e4874", async() => {
                WriteLiteral("请选择API配置");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_0.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
                        </select>
                    </div>
                    <div class=""col-md-6"">
                        <label class=""form-label"">测试操作</label>
                        <div class=""d-flex gap-2"">
                            <button class=""btn btn-primary"" onclick=""testSingleApi()"" id=""testSingleBtn"" disabled>
                                <i class=""fas fa-play me-2""></i>单个测试
                            </button>
                            <button class=""btn btn-success"" onclick=""executeApi()"" id=""executeBtn"" disabled>
                                <i class=""fas fa-rocket me-2""></i>执行API
                            </button>
                            <button class=""btn btn-info"" onclick=""testConnection()"" id=""testConnectionBtn"" disabled>
                                <i class=""fas fa-link me-2""></i>连接测试
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批");
            WriteLiteral(@"量测试 -->
<div class=""row mb-4"">
    <div class=""col-12"">
        <div class=""card"">
            <div class=""card-header"">
                <h6 class=""m-0 font-weight-bold text-primary"">批量测试</h6>
            </div>
            <div class=""card-body"">
                <div class=""row"">
                    <div class=""col-md-8"">
                        <label for=""batchApiSelect"" class=""form-label"">选择多个API配置</label>
                        <select class=""form-select"" id=""batchApiSelect"" multiple size=""5"">
                            <!-- 选项将通过JavaScript动态加载 -->
                        </select>
                        <div class=""form-text"">按住Ctrl键可选择多个API</div>
                    </div>
                    <div class=""col-md-4"">
                        <label class=""form-label"">批量操作</label>
                        <div class=""d-grid gap-2"">
                            <button class=""btn btn-warning"" onclick=""batchExecute()"" id=""batchExecuteBtn"">
                                <i class=""fas fa-tasks me-2""></i>批量执");
            WriteLiteral(@"行
                            </button>
                            <button class=""btn btn-secondary"" onclick=""selectAllApis()"">
                                <i class=""fas fa-check-square me-2""></i>全选
                            </button>
                            <button class=""btn btn-outline-secondary"" onclick=""clearSelection()"">
                                <i class=""fas fa-times me-2""></i>清空选择
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API详情和参数 -->
<div class=""row mb-4"" id=""apiDetailsSection"" style=""display: none;"">
    <div class=""col-12"">
        <div class=""card"">
            <div class=""card-header"">
                <h6 class=""m-0 font-weight-bold text-primary"">API详情</h6>
            </div>
            <div class=""card-body"">
                <div class=""row"">
                    <div class=""col-md-6"">
                        <table class=""table table-sm"">
     ");
            WriteLiteral(@"                       <tr>
                                <td><strong>名称:</strong></td>
                                <td id=""apiName"">-</td>
                            </tr>
                            <tr>
                                <td><strong>URL:</strong></td>
                                <td id=""apiUrl"">-</td>
                            </tr>
                            <tr>
                                <td><strong>方法:</strong></td>
                                <td id=""apiMethod"">-</td>
                            </tr>
                            <tr>
                                <td><strong>超时:</strong></td>
                                <td id=""apiTimeout"">-</td>
                            </tr>
                        </table>
                    </div>
                    <div class=""col-md-6"">
                        <table class=""table table-sm"">
                            <tr>
                                <td><strong>重试次数:</strong></td>
                             ");
            WriteLiteral(@"   <td id=""apiRetryCount"">-</td>
                            </tr>
                            <tr>
                                <td><strong>状态:</strong></td>
                                <td id=""apiStatus"">-</td>
                            </tr>
                            <tr>
                                <td><strong>认证:</strong></td>
                                <td id=""apiAuth"">-</td>
                            </tr>
                            <tr>
                                <td><strong>描述:</strong></td>
                                <td id=""apiDescription"">-</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- API参数 -->
                <div class=""mt-3"" id=""apiParametersSection"">
                    <h6>API参数</h6>
                    <div id=""apiParameters"">
                        <div class=""text-muted"">暂无参数</div>
                    </div>
                </div>
            ");
            WriteLiteral(@"</div>
        </div>
    </div>
</div>

<!-- 测试结果 -->
<div class=""row mb-4"">
    <div class=""col-12"">
        <div class=""card"">
            <div class=""card-header d-flex justify-content-between align-items-center"">
                <h6 class=""m-0 font-weight-bold text-primary"">测试结果</h6>
                <div>
                    <button class=""btn btn-sm btn-outline-secondary"" onclick=""clearResults()"">
                        <i class=""fas fa-trash me-2""></i>清空结果
                    </button>
                    <button class=""btn btn-sm btn-outline-primary"" onclick=""exportResults()"">
                        <i class=""fas fa-download me-2""></i>导出结果
                    </button>
                </div>
            </div>
            <div class=""card-body"">
                <div id=""testResults"">
                    <div class=""text-center text-muted"">
                        <i class=""fas fa-flask fa-3x mb-3""></i>
                        <p>点击测试按钮开始API测试</p>
                    </div>
                </div>
   ");
            WriteLiteral(@"         </div>
        </div>
    </div>
</div>

<!-- 执行历史 -->
<div class=""row"">
    <div class=""col-12"">
        <div class=""card"">
            <div class=""card-header d-flex justify-content-between align-items-center"">
                <h6 class=""m-0 font-weight-bold text-primary"">最近执行历史</h6>
                <button class=""btn btn-sm btn-outline-primary"" onclick=""refreshHistory()"">
                    <i class=""fas fa-sync-alt me-2""></i>刷新
                </button>
            </div>
            <div class=""card-body"">
                <div class=""table-responsive"">
                    <table class=""table table-bordered table-sm"" id=""executionHistoryTable"">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>API名称</th>
                                <th>状态</th>
                                <th>响应时间</th>
                                <th>执行类型</th>
                                <th>操作</th>
                     ");
            WriteLiteral(@"       </tr>
                        </thead>
                        <tbody id=""executionHistoryTableBody"">
                            <tr>
                                <td colspan=""6"" class=""text-center"">
                                    <i class=""fas fa-spinner fa-spin""></i>
                                    <span class=""ms-2"">加载中...</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 结果详情模态框 -->
<div class=""modal fade"" id=""resultDetailModal"" tabindex=""-1"" aria-labelledby=""resultDetailModalLabel"" aria-hidden=""true"">
    <div class=""modal-dialog modal-xl"">
        <div class=""modal-content"">
            <div class=""modal-header"">
                <h5 class=""modal-title"" id=""resultDetailModalLabel"">执行结果详情</h5>
                <button type=""button"" class=""btn-close"" data-bs-dismiss=""modal"" aria-label=""Close""></button>
            </di");
            WriteLiteral(@"v>
            <div class=""modal-body"" id=""resultDetailContent"">
                <!-- 详情内容将通过JavaScript动态加载 -->
            </div>
            <div class=""modal-footer"">
                <button type=""button"" class=""btn btn-secondary"" data-bs-dismiss=""modal"">关闭</button>
                <button type=""button"" class=""btn btn-primary"" onclick=""copyResultToClipboard()"">
                    <i class=""fas fa-copy me-2""></i>复制结果
                </button>
            </div>
        </div>
    </div>
</div>

");
            DefineSection("Scripts", async() => {
                WriteLiteral(@"
<script>
    let currentApiConfig = null;
    let testResults = [];

    $(document).ready(function() {
        loadApiConfigurations();
        loadExecutionHistory();
        
        $('#apiSelect').on('change', function() {
            var selectedApiId = $(this).val();
            if (selectedApiId) {
                loadApiDetails(selectedApiId);
                enableTestButtons(true);
            } else {
                hideApiDetails();
                enableTestButtons(false);
            }
        });
    });

    function loadApiConfigurations() {
        callAPI('/api/ApiConfiguration')
            .done(function(data) {
                var apiSelect = $('#apiSelect');
                var batchSelect = $('#batchApiSelect');
                
                apiSelect.empty().append('<option value="""">请选择API配置</option>');
                batchSelect.empty();
                
                if (data && data.length > 0) {
                    data.forEach(function(api) {
                        var ");
                WriteLiteral(@"option = `<option value=""${api.id}"">${api.name} (${api.method})</option>`;
                        apiSelect.append(option);
                        batchSelect.append(option);
                    });
                }
            })
            .fail(function() {
                showError('加载API配置失败');
            });
    }

    function loadApiDetails(apiId) {
        callAPI('/api/ApiConfiguration/' + apiId)
            .done(function(api) {
                currentApiConfig = api;
                displayApiDetails(api);
                $('#apiDetailsSection').show();
            })
            .fail(function() {
                showError('加载API详情失败');
            });
    }

    function displayApiDetails(api) {
        $('#apiName').text(api.name || '-');
        $('#apiUrl').text((api.baseUrl || '') + (api.endpoint || ''));
        $('#apiMethod').html(`<span class=""badge bg-primary"">${api.method || 'GET'}</span>`);
        $('#apiTimeout').text((api.timeoutSeconds || 30) + '秒');
        $('#apiRetryCount");
                WriteLiteral(@"').text(api.retryCount || 0);
        $('#apiStatus').html(api.isEnabled ? '<span class=""badge bg-success"">启用</span>' : '<span class=""badge bg-secondary"">禁用</span>');
        $('#apiAuth').text(api.authenticationConfigId ? '已配置' : '无');
        $('#apiDescription').text(api.description || '-');
        
        // 显示参数
        displayApiParameters(api.parameters || []);
    }

    function displayApiParameters(parameters) {
        var container = $('#apiParameters');
        container.empty();
        
        if (!parameters || parameters.length === 0) {
            container.html('<div class=""text-muted"">暂无参数</div>');
            return;
        }

        var table = `
            <table class=""table table-sm table-bordered"">
                <thead>
                    <tr>
                        <th>参数名</th>
                        <th>位置</th>
                        <th>类型</th>
                        <th>值</th>
                        <th>必填</th>
                        <th>描述</th>
                   ");
                WriteLiteral(@" </tr>
                </thead>
                <tbody>
        `;
        
        parameters.forEach(function(param) {
            var location = getParameterLocation(param.location);
            var type = getParameterType(param.type);
            var required = param.isRequired ? '<span class=""badge bg-danger"">是</span>' : '<span class=""badge bg-secondary"">否</span>';
            
            table += `
                <tr>
                    <td><code>${param.name}</code></td>
                    <td>${location}</td>
                    <td>${type}</td>
                    <td>${param.value || param.format || '-'}</td>
                    <td>${required}</td>
                    <td>${param.description || '-'}</td>
                </tr>
            `;
        });
        
        table += '</tbody></table>';
        container.html(table);
    }

    function getParameterLocation(location) {
        switch (location) {
            case 'query': return 'Query参数';
            case 'header': return 'Header';
");
                WriteLiteral(@"            case 'body': return 'Body';
            case 'path': return 'Path参数';
            default: return location || '-';
        }
    }

    function getParameterType(type) {
        switch (type) {
            case 1: return '固定值';
            case 2: return '当前时间';
            case 3: return '时间戳';
            case 4: return '上次执行时间';
            case 99: return '动态值';
            default: return '未知';
        }
    }

    function hideApiDetails() {
        $('#apiDetailsSection').hide();
        currentApiConfig = null;
    }

    function enableTestButtons(enabled) {
        $('#testSingleBtn').prop('disabled', !enabled);
        $('#executeBtn').prop('disabled', !enabled);
        $('#testConnectionBtn').prop('disabled', !enabled);
    }

    function testSingleApi() {
        if (!currentApiConfig) {
            showError('请先选择API配置');
            return;
        }

        showInfo('正在测试API连接...');
        var startTime = new Date();

        callAPI('/api/ApiExecution/' + currentApiConfig.id +");
                WriteLiteral(@" '/test', 'POST')
            .done(function(result) {
                var endTime = new Date();
                var duration = endTime - startTime;

                var testResult = {
                    id: Date.now(),
                    apiName: currentApiConfig.name,
                    type: '连接测试',
                    success: result.success,
                    duration: duration,
                    timestamp: new Date(),
                    details: result
                };

                addTestResult(testResult);

                if (result.success) {
                    showSuccess('API连接测试成功');
                } else {
                    showWarning('API连接测试失败');
                }
            })
            .fail(function(xhr) {
                var endTime = new Date();
                var duration = endTime - startTime;

                var testResult = {
                    id: Date.now(),
                    apiName: currentApiConfig.name,
                    type: '连接测试',
               ");
                WriteLiteral(@"     success: false,
                    duration: duration,
                    timestamp: new Date(),
                    error: xhr.responseJSON ? xhr.responseJSON.message : '请求失败'
                };

                addTestResult(testResult);
                showError('API连接测试请求失败');
            });
    }

    function executeApi() {
        if (!currentApiConfig) {
            showError('请先选择API配置');
            return;
        }

        showInfo('正在执行API...');
        var startTime = new Date();

        callAPI('/api/ApiExecution/' + currentApiConfig.id + '/execute', 'POST')
            .done(function(result) {
                var endTime = new Date();
                var duration = endTime - startTime;

                var testResult = {
                    id: Date.now(),
                    apiName: currentApiConfig.name,
                    type: 'API执行',
                    success: result.isSuccess,
                    duration: duration,
                    timestamp: new Date(),
              ");
                WriteLiteral(@"      details: result
                };

                addTestResult(testResult);

                if (result.isSuccess) {
                    showSuccess('API执行成功');
                } else {
                    showWarning('API执行失败: ' + (result.errorMessage || '未知错误'));
                }

                // 刷新执行历史
                loadExecutionHistory();
            })
            .fail(function(xhr) {
                var endTime = new Date();
                var duration = endTime - startTime;

                var testResult = {
                    id: Date.now(),
                    apiName: currentApiConfig.name,
                    type: 'API执行',
                    success: false,
                    duration: duration,
                    timestamp: new Date(),
                    error: xhr.responseJSON ? xhr.responseJSON.message : '请求失败'
                };

                addTestResult(testResult);
                showError('API执行请求失败');
            });
    }

    function testConnection() {
     ");
                WriteLiteral(@"   testSingleApi(); // 连接测试和单个测试是同一个功能
    }

    function batchExecute() {
        var selectedApis = $('#batchApiSelect').val();
        if (!selectedApis || selectedApis.length === 0) {
            showError('请选择要执行的API配置');
            return;
        }

        showInfo(`正在批量执行 ${selectedApis.length} 个API...`);
        var startTime = new Date();

        callAPI('/api/ApiExecution/batch-execute', 'POST', selectedApis)
            .done(function(results) {
                var endTime = new Date();
                var duration = endTime - startTime;

                var successCount = results.filter(r => r.isSuccess).length;
                var failureCount = results.length - successCount;

                var batchResult = {
                    id: Date.now(),
                    apiName: `批量执行 (${results.length}个API)`,
                    type: '批量执行',
                    success: failureCount === 0,
                    duration: duration,
                    timestamp: new Date(),
                    d");
                WriteLiteral(@"etails: {
                        total: results.length,
                        success: successCount,
                        failure: failureCount,
                        results: results
                    }
                };

                addTestResult(batchResult);

                if (failureCount === 0) {
                    showSuccess(`批量执行完成，全部 ${successCount} 个API执行成功`);
                } else {
                    showWarning(`批量执行完成，${successCount} 个成功，${failureCount} 个失败`);
                }

                // 刷新执行历史
                loadExecutionHistory();
            })
            .fail(function(xhr) {
                var endTime = new Date();
                var duration = endTime - startTime;

                var batchResult = {
                    id: Date.now(),
                    apiName: `批量执行 (${selectedApis.length}个API)`,
                    type: '批量执行',
                    success: false,
                    duration: duration,
                    timestamp: new Date(),
    ");
                WriteLiteral(@"                error: xhr.responseJSON ? xhr.responseJSON.message : '批量执行请求失败'
                };

                addTestResult(batchResult);
                showError('批量执行请求失败');
            });
    }

    function selectAllApis() {
        $('#batchApiSelect option').prop('selected', true);
    }

    function clearSelection() {
        $('#batchApiSelect option').prop('selected', false);
    }

    function addTestResult(result) {
        testResults.unshift(result); // 添加到开头

        // 限制结果数量
        if (testResults.length > 50) {
            testResults = testResults.slice(0, 50);
        }

        displayTestResults();
    }

    function displayTestResults() {
        var container = $('#testResults');

        if (testResults.length === 0) {
            container.html(`
                <div class=""text-center text-muted"">
                    <i class=""fas fa-flask fa-3x mb-3""></i>
                    <p>点击测试按钮开始API测试</p>
                </div>
            `);
            return;
        }

      ");
                WriteLiteral(@"  var html = '';
        testResults.forEach(function(result) {
            var statusIcon = result.success ? 'fa-check-circle text-success' : 'fa-times-circle text-danger';
            var statusText = result.success ? '成功' : '失败';

            html += `
                <div class=""border rounded p-3 mb-3"">
                    <div class=""d-flex justify-content-between align-items-start"">
                        <div class=""flex-grow-1"">
                            <div class=""d-flex align-items-center mb-2"">
                                <i class=""fas ${statusIcon} me-2""></i>
                                <strong>${result.apiName}</strong>
                                <span class=""badge bg-secondary ms-2"">${result.type}</span>
                                <span class=""badge ${result.success ? 'bg-success' : 'bg-danger'} ms-2"">${statusText}</span>
                            </div>
                            <div class=""small text-muted"">
                                <i class=""fas fa-clock me-1");
                WriteLiteral(@"""></i>执行时间: ${result.timestamp.toLocaleString('zh-CN')}
                                <span class=""ms-3""><i class=""fas fa-stopwatch me-1""></i>耗时: ${result.duration}ms</span>
                            </div>
                            ${result.error ? `<div class=""small text-danger mt-1""><i class=""fas fa-exclamation-triangle me-1""></i>${result.error}</div>` : ''}
                        </div>
                        <div>
                            <button class=""btn btn-sm btn-outline-primary"" onclick=""showResultDetail(${result.id})"">
                                <i class=""fas fa-eye""></i> 详情
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });

        container.html(html);
    }

    function showResultDetail(resultId) {
        var result = testResults.find(r => r.id === resultId);
        if (!result) return;

        var content = `
            <div class=""row"">
                <div class=""col-md-6"">
  ");
                WriteLiteral(@"                  <h6>基本信息</h6>
                    <table class=""table table-sm"">
                        <tr><td><strong>API名称:</strong></td><td>${result.apiName}</td></tr>
                        <tr><td><strong>测试类型:</strong></td><td>${result.type}</td></tr>
                        <tr><td><strong>执行状态:</strong></td><td>${result.success ? '<span class=""badge bg-success"">成功</span>' : '<span class=""badge bg-danger"">失败</span>'}</td></tr>
                        <tr><td><strong>执行时间:</strong></td><td>${result.timestamp.toLocaleString('zh-CN')}</td></tr>
                        <tr><td><strong>响应时间:</strong></td><td>${result.duration}ms</td></tr>
                    </table>
                </div>
                <div class=""col-md-6"">
                    <h6>详细结果</h6>
                    <pre class=""bg-light p-2 border rounded"" style=""max-height: 300px; overflow-y: auto;"">${JSON.stringify(result.details || result.error || {}, null, 2)}</pre>
                </div>
            </div>
        `;

        if (re");
                WriteLiteral(@"sult.type === '批量执行' && result.details && result.details.results) {
            content += `
                <div class=""row mt-3"">
                    <div class=""col-12"">
                        <h6>批量执行详情</h6>
                        <div class=""table-responsive"">
                            <table class=""table table-sm table-bordered"">
                                <thead>
                                    <tr>
                                        <th>API</th>
                                        <th>状态</th>
                                        <th>响应时间</th>
                                        <th>错误信息</th>
                                    </tr>
                                </thead>
                                <tbody>
            `;

            result.details.results.forEach(function(apiResult) {
                var status = apiResult.isSuccess ? '<span class=""badge bg-success"">成功</span>' : '<span class=""badge bg-danger"">失败</span>';
                content += `
                ");
                WriteLiteral(@"    <tr>
                        <td>${apiResult.apiName || '-'}</td>
                        <td>${status}</td>
                        <td>${apiResult.executionTimeMs || '-'}ms</td>
                        <td>${apiResult.errorMessage || '-'}</td>
                    </tr>
                `;
            });

            content += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }

        $('#resultDetailContent').html(content);
        $('#resultDetailModal').modal('show');
    }

    function clearResults() {
        if (testResults.length === 0) {
            showInfo('没有测试结果需要清空');
            return;
        }

        if (confirm('确定要清空所有测试结果吗？')) {
            testResults = [];
            displayTestResults();
            showSuccess('测试结果已清空');
        }
    }

    function exportResults() {
        if (testResults.length === 0) {
            showWarning('没有测试结果可以导");
                WriteLiteral(@"出');
            return;
        }

        var csvContent = ""时间,API名称,测试类型,状态,响应时间(ms),错误信息\n"";

        testResults.forEach(function(result) {
            var row = [
                result.timestamp.toLocaleString('zh-CN'),
                result.apiName,
                result.type,
                result.success ? '成功' : '失败',
                result.duration,
                result.error || ''
            ].map(field => `""${field}""`).join(',');

            csvContent += row + ""\n"";
        });

        var blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        var link = document.createElement(""a"");
        var url = URL.createObjectURL(blob);
        link.setAttribute(""href"", url);
        link.setAttribute(""download"", `api_test_results_${new Date().toISOString().slice(0, 10)}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showSuccess('测试结果已导出');
    }

    function lo");
                WriteLiteral(@"adExecutionHistory() {
        callAPI('/api/ApiExecutionLog/query?limit=20')
            .done(function(data) {
                displayExecutionHistory(data);
            })
            .fail(function() {
                $('#executionHistoryTableBody').html('<tr><td colspan=""6"" class=""text-center text-danger"">加载失败</td></tr>');
            });
    }

    function displayExecutionHistory(logs) {
        var tbody = $('#executionHistoryTableBody');
        tbody.empty();

        if (!logs || logs.length === 0) {
            tbody.html('<tr><td colspan=""6"" class=""text-center text-muted"">暂无执行历史</td></tr>');
            return;
        }

        logs.forEach(function(log) {
            var statusBadge = log.isSuccess
                ? '<span class=""badge bg-success"">成功</span>'
                : '<span class=""badge bg-danger"">失败</span>';

            var executionType = log.executionType === 'Manual' ? '手动执行' : '自动执行';

            var row = `
                <tr>
                    <td>${formatDateTime(log.star");
                WriteLiteral(@"tTime)}</td>
                    <td>${log.apiName || '-'}</td>
                    <td>${statusBadge}</td>
                    <td>${log.executionTimeMs || '-'}ms</td>
                    <td>${executionType}</td>
                    <td>
                        <button class=""btn btn-sm btn-outline-primary"" onclick=""showExecutionDetail('${log.id}')"">
                            <i class=""fas fa-eye""></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    function showExecutionDetail(logId) {
        callAPI('/api/ApiExecutionLog/' + logId)
            .done(function(log) {
                var content = `
                    <div class=""row"">
                        <div class=""col-md-6"">
                            <h6>执行信息</h6>
                            <table class=""table table-sm"">
                                <tr><td><strong>API名称:</strong></td><td>${log.apiName || '-'}</td></tr>
                    ");
                WriteLiteral(@"            <tr><td><strong>执行状态:</strong></td><td>${log.isSuccess ? '<span class=""badge bg-success"">成功</span>' : '<span class=""badge bg-danger"">失败</span>'}</td></tr>
                                <tr><td><strong>开始时间:</strong></td><td>${formatDateTime(log.startTime)}</td></tr>
                                <tr><td><strong>结束时间:</strong></td><td>${formatDateTime(log.endTime)}</td></tr>
                                <tr><td><strong>响应时间:</strong></td><td>${log.executionTimeMs || '-'}ms</td></tr>
                                <tr><td><strong>执行类型:</strong></td><td>${log.executionType === 'Manual' ? '手动执行' : '自动执行'}</td></tr>
                            </table>
                        </div>
                        <div class=""col-md-6"">
                            <h6>错误信息</h6>
                            <pre class=""bg-light p-2 border rounded"" style=""max-height: 200px; overflow-y: auto;"">${log.errorMessage || '无错误'}</pre>
                        </div>
                    </div>
                    <");
                WriteLiteral(@"div class=""row mt-3"">
                        <div class=""col-12"">
                            <h6>响应数据</h6>
                            <pre class=""bg-light p-2 border rounded"" style=""max-height: 300px; overflow-y: auto;"">${JSON.stringify(log.responseData || {}, null, 2)}</pre>
                        </div>
                    </div>
                `;

                $('#resultDetailContent').html(content);
                $('#resultDetailModal').modal('show');
            })
            .fail(function() {
                showError('加载执行详情失败');
            });
    }

    function refreshHistory() {
        loadExecutionHistory();
        showSuccess('执行历史已刷新');
    }

    function copyResultToClipboard() {
        var content = $('#resultDetailContent').text();
        navigator.clipboard.writeText(content).then(function() {
            showSuccess('结果已复制到剪贴板');
        }).catch(function() {
            showWarning('复制失败，请手动选择复制');
        });
    }

    function formatDateTime(dateTime) {
        if (!da");
                WriteLiteral("teTime) return \'-\';\n        return new Date(dateTime).toLocaleString(\'zh-CN\');\n    }\n</script>\n");
            }
            );
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591

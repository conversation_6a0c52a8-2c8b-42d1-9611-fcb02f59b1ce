<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewBag.Title - 第三方接口集成管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .navbar-brand {
            font-weight: bold;
        }
        .status-badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">
                            <i class="fas fa-plug me-2"></i>
                            接口集成系统
                        </h5>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/api-configs">
                                <i class="fas fa-cogs me-2"></i>
                                API配置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/data-mapping">
                                <i class="fas fa-exchange-alt me-2"></i>
                                数据映射
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/scheduler">
                                <i class="fas fa-clock me-2"></i>
                                任务调度
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/execution-history">
                                <i class="fas fa-history me-2"></i>
                                执行历史
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/monitoring">
                                <i class="fas fa-chart-line me-2"></i>
                                系统监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/api-test">
                                <i class="fas fa-flask me-2"></i>
                                API测试
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">
                                <i class="fas fa-sliders-h me-2"></i>
                                系统设置
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="my-3">
                    
                    <div class="text-center">
                        <small class="text-white-50">
                            <i class="fas fa-info-circle me-1"></i>
                            版本 2.0.0
                        </small>
                    </div>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航栏 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">@ViewBag.Title</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="fas fa-sync-alt me-1"></i>
                                刷新
                            </button>
                            <a href="/swagger" class="btn btn-sm btn-outline-primary" target="_blank">
                                <i class="fas fa-book me-1"></i>
                                API文档
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 页面内容 -->
                @RenderBody()
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 通用JavaScript -->
    <script>
        // 设置当前页面的导航链接为活动状态
        $(document).ready(function() {
            var currentPath = window.location.pathname;
            $('.sidebar .nav-link').each(function() {
                if ($(this).attr('href') === currentPath) {
                    $(this).addClass('active');
                }
            });
        });

        // 通用API调用函数
        function callAPI(url, method = 'GET', data = null) {
            return $.ajax({
                url: url,
                method: method,
                data: data ? JSON.stringify(data) : null,
                contentType: 'application/json',
                dataType: 'json'
            });
        }

        // 显示成功消息
        function showSuccess(message) {
            showAlert(message, 'success');
        }

        // 显示错误消息
        function showError(message) {
            showAlert(message, 'danger');
        }

        // 显示警告消息
        function showWarning(message) {
            showAlert(message, 'warning');
        }

        // 显示通用警告框
        function showAlert(message, type) {
            var alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('#alerts-container').html(alertHtml);
            
            // 3秒后自动隐藏
            setTimeout(function() {
                $('.alert').alert('close');
            }, 3000);
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return '-';
            var date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            var k = 1024;
            var sizes = ['Bytes', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>

    @RenderSection("Scripts", required: false)
</body>
</html>
